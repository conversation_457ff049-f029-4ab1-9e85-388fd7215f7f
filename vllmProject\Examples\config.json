{"app": {"name": "GPTOSS", "name_zh": "大模型应用", "host": "0.0.0.0", "port": 5000, "version": "1.0.0", "description": "A large model application"}, "register": {"proxy_client_name": "MGS86", "proxy_client_group": "MGS", "proxy_client_port": 8090, "proxy_server_ip": "*************", "proxy_server_port": 8090, "agent_register_url": "http://*************:8100/AI/Agent/server-node/register", "agent_heatbeat_url": "http://*************:8100/AI/Agent/server-node/heartbeat"}, "provider": {"name": "mgs", "phone": "xxxxxxxxxxx", "email": "<EMAIL>"}, "logging": {"level": "DEBUG", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "filename": "app.log", "max_bytes": 10485760, "backup_count": 5}, "services": {"VllmService": {"name_zh": "vllm 示例服务", "name": "VllmService", "input": {"input_json": "json"}, "parameters": {"models": ["gpt-oss-120b"]}, "output": {"output_json": "json"}, "url": "/chat/completions", "type": "Json", "subtype": "Vllm", "group": "BaseOS.AI.Foundry"}}}