# vLLM Project 部署说明文档

## 项目概述

vLLM Project 是一个基于 vLLM 引擎的大语言模型推理服务项目，提供 OpenAI 兼容的 API 接口，支持高性能的模型推理和服务部署。

## 系统要求

- **操作系统**: Linux (推荐 Ubuntu 18.04+)
- **GPU**: NVIDIA GPU (支持 CUDA)
- **Docker**: Docker 20.10+ 和 Docker Compose
- **内存**: 建议 32GB+ RAM
- **存储**: 足够存储模型文件的磁盘空间

## 镜像信息

### 主要镜像
- **镜像名称**: `vllm:gpt-oss`
- **镜像仓库**: `registry.openwebgis.com`
- **认证信息**:
  - 用户名: `admin`
  - 密码: `Harbor12345`

### 可选镜像版本
```bash
# 官方 vLLM 镜像
registry.openwebgis.com/llm/vllm:1.0
registry.openwebgis.com/llm/vllm:2.0

# AWS ECR 镜像 (指定 commit)
public.ecr.aws/q9t5s3a7/vllm-ci-postmerge-repo:${VLLM_COMMIT}
```

## 环境变量配置

### 必需环境变量

| 变量名 | 描述 | 默认值 | 示例 |
|--------|------|--------|------|
| `SERVER_PORT` | 服务器外部端口 | `60000` | `60000` |
| `HOST_IP` | 主机IP地址 | 自动检测 | `*************` |
| `APP_PORT` | 应用内部端口 | 从config.json读取 | `5000` |
| `APP_NAME` | 应用名称 | 从config.json读取 | `GPTOSS` |

### vLLM 特定环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `VLLM_ATTENTION_BACKEND` | 注意力机制后端 | `TRITON_ATTN_VLLM_V1` |
| `VLLM_COMMIT` | vLLM 版本 commit hash | - |

### Docker Compose 环境变量

项目会自动生成 `.env` 文件，包含以下变量：
```bash
IMAGE_NAME=vllm:gpt-oss
WORK_DIR=/path/to/vllmProject
SERVER_PORT=60000
APP_NAME=GPTOSS
APP_PORT=5000
HOST_IP=*************
```

## 模型配置

### 模型路径
- **模型目录**: `/work/gpt_oss_120b`
- **聊天模板**: `/work/gpt_oss_120b/chat_template.jinja`

### vLLM 启动参数
```bash
python3 -m vllm.entrypoints.openai.api_server \
    --model /work/gpt_oss_120b \
    --tensor-parallel-size 4 \
    --served-model-name gpt-oss-120b \
    --chat-template /work/gpt_oss_120b/chat_template.jinja
```

## 部署方式

### 方式一：使用 Docker Compose (推荐)

1. **拉取镜像并启动服务**:
```bash
cd vllmProject
sudo bash run_as_docker.sh
```

2. **手动启动**:
```bash
# 登录镜像仓库
echo "Harbor12345" | docker login -u admin --password-stdin registry.openwebgis.com

# 拉取镜像
docker pull vllm:gpt-oss

# 启动服务
sudo docker compose up -d
```

### 方式二：直接使用 Docker

```bash
cd vllmProject
docker run -d --rm --gpus all \
    -v "$(pwd)":/work \
    -p 60000:5000 \
    -e SERVER_PORT=60000 \
    -e HOST_IP=$(hostname -I | awk '{print $1}') \
    -e VLLM_ATTENTION_BACKEND=TRITON_ATTN_VLLM_V1 \
    --runtime nvidia \
    vllm:gpt-oss /bin/bash /work/run.sh
```

## 配置文件说明

### config.json 配置
位置: `Examples/config.json`

主要配置项：
```json
{
    "app": {
        "name": "GPTOSS",
        "host": "0.0.0.0",
        "port": 5000
    },
    "services": {
        "VllmService": {
            "name": "VllmService",
            "parameters": {
                "models": ["gpt-oss-120b"]
            },
            "url": "/chat/completions"
        }
    }
}
```

## 服务管理

### 启动服务
```bash
# 使用脚本启动
sudo bash run_as_docker.sh

# 使用 docker-compose
sudo docker compose up -d
```

### 查看服务状态
```bash
# 查看容器状态
docker ps

# 查看服务日志
docker logs -f <container_name>

# 查看 vLLM 日志
tail -f vllmProject/Examples/vllm.log
```

### 停止服务
```bash
# 停止 docker-compose 服务
sudo docker compose down

# 停止特定容器
docker stop <container_name>
```

## API 接口

### 服务端点
- **基础URL**: `http://<HOST_IP>:<SERVER_PORT>`
- **聊天接口**: `/VllmService/chat/completions`
- **健康检查**: `/health`

### 使用示例
```bash
curl -X POST http://localhost:60000/VllmService/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-oss-120b",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "stream": false
  }'
```

## 故障排除

### 常见问题

1. **镜像拉取失败**
   - 检查网络连接
   - 验证镜像仓库认证信息
   - 确认镜像名称和标签正确

2. **GPU 不可用**
   - 安装 NVIDIA Docker runtime
   - 检查 GPU 驱动和 CUDA 版本
   - 验证 `--gpus all` 参数

3. **端口冲突**
   - 修改 `SERVER_PORT` 环境变量
   - 检查端口占用情况: `netstat -tlnp | grep <port>`

4. **内存不足**
   - 增加系统内存
   - 调整模型并行度参数
   - 使用更小的模型

### 日志查看
```bash
# 应用日志
tail -f vllmProject/Examples/service.log

# vLLM 推理日志
tail -f vllmProject/Examples/vllm.log

# Docker 容器日志
docker logs -f <container_name>
```

## 开发和定制

### 项目结构
```
vllmProject/
├── docker-compose.yaml     # Docker Compose 配置
├── run_as_docker.sh       # 启动脚本
├── run.sh                 # 容器内启动脚本
├── Examples/
│   ├── config.json        # 应用配置
│   ├── app.py            # Flask 应用
│   ├── start.sh          # 服务启动脚本
│   └── services/
│       └── VllmService.py # vLLM 服务实现
└── README.md             # 本文档
```

### 自定义配置
1. 修改 `Examples/config.json` 中的应用配置
2. 调整 `Examples/start.sh` 中的 vLLM 启动参数
3. 自定义 `Examples/services/VllmService.py` 中的服务逻辑

## 许可证

本项目遵循相应的开源许可证，具体请查看项目根目录下的 LICENSE 文件。
