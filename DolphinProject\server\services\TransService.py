import dolphin
import os
from flask import jsonify, render_template
from BaseOS.ai.framework import ModelService, SVREGISTRY
from BaseOS.ai.framework.utils import get_logger
logger = get_logger()


@SVREGISTRY.register_module("TransService")
class TransService(ModelService):
    def __init__(self, config, upload_dir):
        """
        初始化服务对象
        :param config: server配置字典对象
        """
        super().__init__(config, upload_dir)

        # 加载模型(可选)
        self.work_model_name = config.get("model_name", "small")
        self.work_model_path = config.get("model_path", "model")
        self.work_model = self.load_model()

    def predict(self, request):
        """
        处理音频文件并返回文本
        :param request: Flask请求对象
        :return: 包含文本的字典
        """

        if 'file' not in request.files:
            return jsonify({'error': 'No file part'}), 400
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400
        if file:
            filename = file.filename
            filepath = os.path.join(self.upload_dir, filename)
            file.save(filepath)
            try:
                text = self._process_audio(filepath)
                return jsonify({'text': text})
            except Exception as e:
                return jsonify({'text': ""})
            finally:
                remove_file(filepath)

    def health_check(self):
        """
        检查服务的健康状态
        :return: 包含状态信息的字典
        """
        return {"status": "ok"}

    def metadata(self):
        """
        返回服务的元数据
        :return: 包含元数据信息的字典
        """
        return list(self.get_urls().values())

    def demo(self):
        """ 
        返回服务的演示页面或URL
        :return: 包含演示页面或URL的字符串
        """
        return render_template("demo.html")

    def _process_audio(self, filepath):
        """
        自定义函数，用来处理音频文件并返回文本
        :param filepath: 音频文件路径
        :return: 转录文本
        """
        waveform = dolphin.load_audio(filepath)
        result = self.work_model(waveform, lang_sym="zh", region_sym="CN")
        return result.text_nospecial

    def load_model(self):
        return dolphin.load_model(self.work_model_name, self.work_model_path, "cuda")


def remove_file(filepath):
    try:
        os.remove(filepath)
        logger.info(f"file {filepath} removed")
    except FileNotFoundError:
        logger.error(f"file {filepath} not exits")
    except PermissionError:
        logger.error(f" {filepath} permission denied")
    except Exception as e:
        logger.error(f"remove failed: {e}")
