import os
from flask import jsonify, render_template,send_from_directory

from BaseOS.ai.framework import ModelService,SVREGISTRY
from pathlib import Path
from ultralytics import YOLO
import cv2
import numpy as np

script_dir = os.path.dirname(os.path.abspath(__file__))

model = YOLO(Path(os.path.join(script_dir,'yolo8s/pytorch_model.onnx')))

@SVREGISTRY.register_module("FireSmokeDetService")
class FireSmokeDetService(ModelService):
    def __init__(self,config,upload_dir):     
        super().__init__(config,upload_dir)
        self.upload_dir = upload_dir

    def visualize_detection(self, image, bbox, conf, cls,cls_names):
        if len(bbox) == 0:
            return image

        #class_names = {0: "fire", 1: "smoke"}
        if "category" in self.parameters:
            class_names = {int(k): v for k, v in self.parameters["category"].items()}
        else:
            class_names = cls_names
        class_colors = {0: (0, 0, 255), 1: (255, 0, 0)}  # fire: red, smoke: blue

        for n, box in enumerate(bbox):
            if conf[n] <= self.parameters.get('conf', 0.5):
                continue
            class_id = int(cls[n])
            color = class_colors.get(class_id, (0, 255, 0))
            label = f"{class_names.get(class_id, 'unknown')}:{conf[n]:.2f}"

            pt1 = box[:2].astype(np.int32)
            pt2 = box[2:].astype(np.int32)
            image = cv2.rectangle(image, pt1, pt2, color, 3)
            # 在框上方写类别和置信度
            text_size, _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 1, 2)
            text_origin = (pt1[0], pt1[1] - 10 if pt1[1] - 10 > 10 else pt1[1] + 20)
            cv2.rectangle(image, (pt1[0], text_origin[1] - text_size[1] - 4),
                          (pt1[0] + text_size[0], text_origin[1] + 4), color, -1)
            cv2.putText(image, label, (pt1[0], text_origin[1]),
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        return image

    def predict(self, request):
        if 'file' not in request.files:
            return jsonify({'error': 'No file part'}), 400
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400
        if file:
            filename = file.filename
            filepath = os.path.join(self.upload_dir, filename)
            file.save(filepath)
        res = model.predict(filepath)
        #print("res",res)
        #colors = np.random.randint(0, 256, size = (100, 3))
        cls_names = res[0].names
        bbox = res[0].boxes.xyxy.cpu().numpy()
        conf = res[0].boxes.conf.cpu().numpy()
        cls = res[0].boxes.cls.cpu().numpy()  # 获取类别信息
        src = cv2.imread(filepath)
        bbox_list = bbox.tolist()
        new_image = self.visualize_detection(src,bbox,conf,cls,cls_names)
        cv2.imwrite(os.path.join(self.upload_dir, "test.jpg"),new_image)
        return send_from_directory(self.upload_dir, "test.jpg")



