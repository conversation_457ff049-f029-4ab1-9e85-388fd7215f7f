{"app": {"name": "flask-server", "env": "development", "debug": true, "host": "0.0.0.0", "port": 5000, "secret_key": "mgsdxsys", "version": "1.0.0", "description": "A simple Flask server application", "workers": 4, "timeout": 30, "keep_alive": 5, "max_requests": 0}, "register": {"porxy_client_port": 8090, "porxy_server_ip": "*************", "porxy_server_port": 8090, "agent_register_url": "http://*************:8100/AI/Agent/server-node/register", "agent_heatbeat_url": "http://*************:8100/AI/Agent/server-node/heartbeat"}, "logging": {"level": "DEBUG", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "filename": "app.log", "max_bytes": 10485760, "backup_count": 5}, "services": {"TransService": {"name": "TransService", "input_type": "audio", "output_type": "text", "url": "/audio/transcriptions", "type": "BaseOS.AI.Foundry", "group": "Audio", "model_path": "model", "model_name": "small"}}}