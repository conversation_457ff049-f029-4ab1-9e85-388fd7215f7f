2025-08-15 00:01:26,294 - AI-Service - INFO - Started proxy process with PID: 42, cmd: /usr/local/lib/python3.12/dist-packages/BaseOS/ai/proxy/BaseOS.Desktop.TunnelGateway -server ************* -port 8090 -host 127.0.0.1 -manager 8090 -type AI -name MGS86 -group MGS
2025-08-15 00:01:26,298 - AI-Service - INFO - [PROXY] 2025/08/15 00:01:26 Registering client...
2025-08-15 00:01:26,298 - AI-Service - INFO - [PROXY] 2025/08/15 00:01:26 Connecting to server: http://*************:8090/register?name=MGS86&type=AI&id=c03a06f8-4a8b-3273-a77f-f9996ee5fae9&ip=**********&group=MGS
2025-08-15 00:01:26,324 - AI-Service - INFO - [PROXY] 2025/08/15 00:01:26 sever response: &{101 Switching Protocols 101 HTTP/1.1 1 1 map[Connection:[Upgrade] Upgrade:[tcp]] {} 0 [] false false map[] 0xc0002ae000 <nil>}
2025-08-15 00:01:26,325 - AI-Service - INFO - [PROXY] 2025/08/15 00:01:26 successfully registered client and established control connection
2025-08-15 00:01:26,325 - AI-Service - INFO - [PROXY] 2025/08/15 00:01:26 Client registered successfully, building global connection manager...
2025-08-15 00:01:26,325 - AI-Service - INFO - [PROXY] 2025/08/15 00:01:26 Global connection manager built, starting API server...
2025-08-15 00:01:26,325 - AI-Service - INFO - [PROXY] 2025/08/15 00:01:26 API server started at 127.0.0.1:8090
2025-08-15 00:01:36,299 - AI-Service - INFO - Service VllmService bound to URL /VllmService/chat/completions
2025-08-15 00:01:36,299 - AI-Service - INFO - Service VllmService bound to URL /VllmService/healthcheck
2025-08-15 00:01:36,299 - AI-Service - INFO - Service VllmService bound to URL /VllmService/demo
2025-08-15 00:01:36,299 - AI-Service - INFO - Service VllmService bound to URL /VllmService/metadata
2025-08-15 00:01:36,299 - AI-Service - INFO - Service VllmService bound to URL /VllmService/home
2025-08-15 00:01:36,299 - AI-Service - INFO - Registering service VllmService to client at http://localhost:8090/url/register with params: {'app_name': 'GPTOSS', 'service_address': '5000', 'service_name': 'VllmService', 'service_group': 'BaseOS.AI.Foundry', 'base_url': '/VllmService/chat/completions', 'api_type': 'AI'}
2025-08-15 00:01:36,328 - AI-Service - INFO - Service VllmService registered to client at http://localhost:8090/url/register
2025-08-15 00:01:36,328 - AI-Service - INFO - Response: 500 - register url mapping failed: url path already registered by another client: /Tunnel/AI/MGS/GPTOSS/MGS86


2025-08-15 00:01:36,328 - AI-Service - ERROR - Failed to register service VllmService to client: Expecting value: line 1 column 1 (char 0)
2025-08-15 00:01:36,328 - AI-Service - INFO - request agent register body: {'serverName': 'MGS86_GPTOSS', 'aliasName': 'GPTOSS', 'routers': [], 'groupName': 'MGS', 'appName': 'GPTOSS', 'clientName': 'MGS86'}
2025-08-15 00:01:36,329 - AI-Service - INFO - service ip: **************, port: 60000
2025-08-15 00:01:36,329 - AI-Service - ERROR - No routers to register to Agent.
2025-08-15 00:01:56,357 - AI-Service - INFO - [PROXY] 2025/08/15 00:01:56 Received message: Type=pong, ID=heartbeat
2025-08-15 00:02:26,359 - AI-Service - INFO - [PROXY] 2025/08/15 00:02:26 Received message: Type=pong, ID=heartbeat
2025-08-15 00:03:34,210 - AI-Service - INFO - Started proxy process with PID: 42, cmd: /usr/local/lib/python3.12/dist-packages/BaseOS/ai/proxy/BaseOS.Desktop.TunnelGateway -server ************* -port 8090 -host 127.0.0.1 -manager 8090 -type AI -name MGS86 -group MGS
2025-08-15 00:03:34,214 - AI-Service - INFO - [PROXY] 2025/08/15 00:03:34 Registering client...
2025-08-15 00:03:34,215 - AI-Service - INFO - [PROXY] 2025/08/15 00:03:34 Connecting to server: http://*************:8090/register?name=MGS86&type=AI&id=a44ca397-d697-3f81-82bb-15445fef57c6&ip=**********&group=MGS
2025-08-15 00:03:34,230 - AI-Service - INFO - [PROXY] 2025/08/15 00:03:34 sever response: &{101 Switching Protocols 101 HTTP/1.1 1 1 map[Connection:[Upgrade] Upgrade:[tcp]] {} 0 [] false false map[] 0xc0001f6000 <nil>}
2025-08-15 00:03:34,231 - AI-Service - INFO - [PROXY] 2025/08/15 00:03:34 successfully registered client and established control connection
2025-08-15 00:03:34,231 - AI-Service - INFO - [PROXY] 2025/08/15 00:03:34 Client registered successfully, building global connection manager...
2025-08-15 00:03:34,231 - AI-Service - INFO - [PROXY] 2025/08/15 00:03:34 Global connection manager built, starting API server...
2025-08-15 00:03:34,231 - AI-Service - INFO - [PROXY] 2025/08/15 00:03:34 API server started at 127.0.0.1:8090
2025-08-15 00:03:44,218 - AI-Service - INFO - Service VllmService bound to URL /VllmService/chat/completions
2025-08-15 00:03:44,218 - AI-Service - INFO - Service VllmService bound to URL /VllmService/healthcheck
2025-08-15 00:03:44,218 - AI-Service - INFO - Service VllmService bound to URL /VllmService/demo
2025-08-15 00:03:44,219 - AI-Service - INFO - Service VllmService bound to URL /VllmService/metadata
2025-08-15 00:03:44,219 - AI-Service - INFO - Service VllmService bound to URL /VllmService/home
2025-08-15 00:03:44,219 - AI-Service - INFO - Registering service VllmService to client at http://localhost:8090/url/register with params: {'app_name': 'GPTOSS', 'service_address': '5000', 'service_name': 'VllmService', 'service_group': 'BaseOS.AI.Foundry', 'base_url': '/VllmService/chat/completions', 'api_type': 'AI'}
2025-08-15 00:03:44,249 - AI-Service - INFO - [PROXY] 2025/08/15 00:03:44 URL mapping registered successfully: /Tunnel/AI/MGS/GPTOSS/MGS86
2025-08-15 00:03:44,254 - AI-Service - INFO - Service VllmService registered to client at http://localhost:8090/url/register
2025-08-15 00:03:44,254 - AI-Service - INFO - Response: 200 - {"success":true,"url_path":"/Tunnel/AI/MGS/GPTOSS/MGS86"}

2025-08-15 00:03:44,254 - AI-Service - INFO - request agent register body: {'serverName': 'MGS86_GPTOSS', 'aliasName': 'GPTOSS', 'routers': [{'router': '/chat/completions', 'type': 'Json.BaseOS.AI.Foundry.VllmService', 'address': '/Tunnel/AI/MGS/GPTOSS/MGS86/VllmService', 'models': ['gpt-oss-120b']}], 'groupName': 'MGS', 'appName': 'GPTOSS', 'clientName': 'MGS86'}
2025-08-15 00:03:44,255 - AI-Service - INFO - service ip: **************, port: 60000
2025-08-15 00:03:44,369 - AI-Service - INFO - Services registered successfully
2025-08-15 00:03:44,370 - AI-Service - INFO - Heartbeat service started
2025-08-15 00:04:04,258 - AI-Service - INFO - [PROXY] 2025/08/15 00:04:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:04:34,245 - AI-Service - INFO - [PROXY] 2025/08/15 00:04:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:05:04,268 - AI-Service - INFO - [PROXY] 2025/08/15 00:05:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:05:34,266 - AI-Service - INFO - [PROXY] 2025/08/15 00:05:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:06:04,268 - AI-Service - INFO - [PROXY] 2025/08/15 00:06:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:06:34,268 - AI-Service - INFO - [PROXY] 2025/08/15 00:06:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:07:04,367 - AI-Service - INFO - [PROXY] 2025/08/15 00:07:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:07:34,269 - AI-Service - INFO - [PROXY] 2025/08/15 00:07:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:08:04,252 - AI-Service - INFO - [PROXY] 2025/08/15 00:08:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:08:34,239 - AI-Service - INFO - [PROXY] 2025/08/15 00:08:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:09:04,268 - AI-Service - INFO - [PROXY] 2025/08/15 00:09:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:09:34,246 - AI-Service - INFO - [PROXY] 2025/08/15 00:09:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:10:04,243 - AI-Service - INFO - [PROXY] 2025/08/15 00:10:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:10:30,370 - AI-Service - INFO - [PROXY] 2025/08/15 00:10:30 Received message: Type=tcp_open, ID=tcp_1755241830341283100_127.0.0.1:55688
2025-08-15 00:10:30,370 - AI-Service - INFO - [PROXY] 2025/08/15 00:10:30 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:10:34,243 - AI-Service - INFO - [PROXY] 2025/08/15 00:10:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:11:04,268 - AI-Service - INFO - [PROXY] 2025/08/15 00:11:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:11:05,003 - AI-Service - INFO - [PROXY] 2025/08/15 00:11:05 Received message: Type=tcp_open, ID=tcp_1755241864974318600_127.0.0.1:55733
2025-08-15 00:11:05,003 - AI-Service - INFO - [PROXY] 2025/08/15 00:11:05 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:11:34,266 - AI-Service - INFO - [PROXY] 2025/08/15 00:11:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:12:04,268 - AI-Service - INFO - [PROXY] 2025/08/15 00:12:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:12:34,268 - AI-Service - INFO - [PROXY] 2025/08/15 00:12:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:13:04,269 - AI-Service - INFO - [PROXY] 2025/08/15 00:13:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:13:34,269 - AI-Service - INFO - [PROXY] 2025/08/15 00:13:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:13:58,733 - AI-Service - INFO - [PROXY] 2025/08/15 00:13:58 Received message: Type=tcp_open, ID=tcp_1755242038702035200_127.0.0.1:55793
2025-08-15 00:13:58,734 - AI-Service - INFO - [PROXY] 2025/08/15 00:13:58 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:14:04,243 - AI-Service - INFO - [PROXY] 2025/08/15 00:14:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:14:34,248 - AI-Service - INFO - [PROXY] 2025/08/15 00:14:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:14:59,073 - AI-Service - INFO - [PROXY] 2025/08/15 00:14:59 Received message: Type=tcp_open, ID=tcp_1755242099041334100_127.0.0.1:55806
2025-08-15 00:14:59,073 - AI-Service - INFO - [PROXY] 2025/08/15 00:14:59 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:15:04,243 - AI-Service - INFO - [PROXY] 2025/08/15 00:15:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:15:34,268 - AI-Service - INFO - [PROXY] 2025/08/15 00:15:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:16:04,269 - AI-Service - INFO - [PROXY] 2025/08/15 00:16:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:16:34,269 - AI-Service - INFO - [PROXY] 2025/08/15 00:16:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:17:04,251 - AI-Service - INFO - [PROXY] 2025/08/15 00:17:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:17:34,244 - AI-Service - INFO - [PROXY] 2025/08/15 00:17:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:18:04,267 - AI-Service - INFO - [PROXY] 2025/08/15 00:18:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:18:34,268 - AI-Service - INFO - [PROXY] 2025/08/15 00:18:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:19:04,262 - AI-Service - INFO - [PROXY] 2025/08/15 00:19:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:19:34,249 - AI-Service - INFO - [PROXY] 2025/08/15 00:19:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:20:04,271 - AI-Service - INFO - [PROXY] 2025/08/15 00:20:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:20:34,269 - AI-Service - INFO - [PROXY] 2025/08/15 00:20:34 Received message: Type=pong, ID=heartbeat
2025-08-15 00:21:04,247 - AI-Service - INFO - [PROXY] 2025/08/15 00:21:04 Received message: Type=pong, ID=heartbeat
2025-08-15 00:22:39,932 - AI-Service - INFO - Started proxy process with PID: 42, cmd: /usr/local/lib/python3.12/dist-packages/BaseOS/ai/proxy/BaseOS.Desktop.TunnelGateway -server ************* -port 8090 -host 127.0.0.1 -manager 8090 -type AI -name MGS86 -group MGS
2025-08-15 00:22:39,937 - AI-Service - INFO - [PROXY] 2025/08/15 00:22:39 Registering client...
2025-08-15 00:22:39,937 - AI-Service - INFO - [PROXY] 2025/08/15 00:22:39 Connecting to server: http://*************:8090/register?name=MGS86&type=AI&id=f3f670cb-25b0-3ccd-a5c6-1c49ae868bb5&ip=**********&group=MGS
2025-08-15 00:22:39,956 - AI-Service - INFO - [PROXY] 2025/08/15 00:22:39 sever response: &{101 Switching Protocols 101 HTTP/1.1 1 1 map[Connection:[Upgrade] Upgrade:[tcp]] {} 0 [] false false map[] 0xc0001f6000 <nil>}
2025-08-15 00:22:39,957 - AI-Service - INFO - [PROXY] 2025/08/15 00:22:39 successfully registered client and established control connection
2025-08-15 00:22:39,957 - AI-Service - INFO - [PROXY] 2025/08/15 00:22:39 Client registered successfully, building global connection manager...
2025-08-15 00:22:39,957 - AI-Service - INFO - [PROXY] 2025/08/15 00:22:39 Global connection manager built, starting API server...
2025-08-15 00:22:39,957 - AI-Service - INFO - [PROXY] 2025/08/15 00:22:39 API server started at 127.0.0.1:8090
2025-08-15 00:22:49,936 - AI-Service - INFO - Service VllmService bound to URL /VllmService/chat/completions
2025-08-15 00:22:49,936 - AI-Service - INFO - Service VllmService bound to URL /VllmService/healthcheck
2025-08-15 00:22:49,936 - AI-Service - INFO - Service VllmService bound to URL /VllmService/demo
2025-08-15 00:22:49,936 - AI-Service - INFO - Service VllmService bound to URL /VllmService/metadata
2025-08-15 00:22:49,936 - AI-Service - INFO - Service VllmService bound to URL /VllmService/home
2025-08-15 00:22:49,936 - AI-Service - INFO - Registering service VllmService to client at http://localhost:8090/url/register with params: {'app_name': 'GPTOSS', 'service_address': '5000', 'service_name': 'VllmService', 'service_group': 'BaseOS.AI.Foundry', 'base_url': '/VllmService/chat/completions', 'api_type': 'AI'}
2025-08-15 00:22:49,960 - AI-Service - INFO - [PROXY] 2025/08/15 00:22:49 URL mapping registered successfully: /Tunnel/AI/MGS/GPTOSS/MGS86
2025-08-15 00:22:49,961 - AI-Service - INFO - Service VllmService registered to client at http://localhost:8090/url/register
2025-08-15 00:22:49,961 - AI-Service - INFO - Response: 200 - {"success":true,"url_path":"/Tunnel/AI/MGS/GPTOSS/MGS86"}

2025-08-15 00:22:49,962 - AI-Service - INFO - request agent register body: {'serverName': 'MGS86_GPTOSS', 'aliasName': 'GPTOSS', 'routers': [{'router': '/chat/completions', 'type': 'Json.BaseOS.AI.Foundry.VllmService', 'address': '/Tunnel/AI/MGS/GPTOSS/MGS86/VllmService', 'models': ['gpt-oss-120b']}], 'groupName': 'MGS', 'appName': 'GPTOSS', 'clientName': 'MGS86'}
2025-08-15 00:22:49,962 - AI-Service - INFO - service ip: **************, port: 60000
2025-08-15 00:22:50,015 - AI-Service - INFO - Services registered successfully
2025-08-15 00:22:50,016 - AI-Service - INFO - Heartbeat service started
2025-08-15 00:23:09,986 - AI-Service - INFO - [PROXY] 2025/08/15 00:23:09 Received message: Type=pong, ID=heartbeat
2025-08-15 00:23:39,996 - AI-Service - INFO - [PROXY] 2025/08/15 00:23:39 Received message: Type=pong, ID=heartbeat
2025-08-15 00:23:56,393 - AI-Service - INFO - [PROXY] 2025/08/15 00:23:56 Received message: Type=tcp_open, ID=tcp_1755242636354049300_127.0.0.1:55917
2025-08-15 00:23:56,393 - AI-Service - INFO - [PROXY] 2025/08/15 00:23:56 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:23:56,505 - AI-Service - ERROR - Error processing JSON: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x76d50629a8d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-15 00:24:09,979 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:09 Received message: Type=pong, ID=heartbeat
2025-08-15 00:24:39,985 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:39 Received message: Type=pong, ID=heartbeat
2025-08-15 00:24:40,843 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:40 Global connection read error: read tcp **********:55280->*************:8090: read: connection reset by peer
2025-08-15 00:24:40,843 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:40 message process goroutine exited, connection lost
2025-08-15 00:24:40,844 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:40 connection lost, starting reconnect mechanism...
2025-08-15 00:24:40,844 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:40 Trying to reconnect to server...
2025-08-15 00:24:40,844 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:40 Connecting to server: http://*************:8090/register?name=MGS86&type=AI&id=f3f670cb-25b0-3ccd-a5c6-1c49ae868bb5&ip=**********&group=MGS
2025-08-15 00:24:42,934 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:42 sever response: &{101 Switching Protocols 101 HTTP/1.1 1 1 map[Connection:[Upgrade] Upgrade:[tcp]] {} 0 [] false false map[] 0xc0001608c0 <nil>}
2025-08-15 00:24:42,935 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:42 successfully registered client and established control connection
2025-08-15 00:24:42,935 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:42 Reconnect succeeded, updating global connection and connection manager
2025-08-15 00:24:42,935 - AI-Service - INFO - [PROXY] 2025/08/15 00:24:42 Reconnect complete, service is back to normal
2025-08-15 00:25:09,993 - AI-Service - INFO - [PROXY] 2025/08/15 00:25:09 Received message: Type=pong, ID=heartbeat
2025-08-15 00:25:12,946 - AI-Service - INFO - [PROXY] 2025/08/15 00:25:12 Received message: Type=pong, ID=heartbeat
2025-08-15 00:25:39,993 - AI-Service - INFO - [PROXY] 2025/08/15 00:25:39 Received message: Type=pong, ID=heartbeat
2025-08-15 00:25:42,946 - AI-Service - INFO - [PROXY] 2025/08/15 00:25:42 Received message: Type=pong, ID=heartbeat
2025-08-15 00:26:09,993 - AI-Service - INFO - [PROXY] 2025/08/15 00:26:09 Received message: Type=pong, ID=heartbeat
2025-08-15 00:26:12,952 - AI-Service - INFO - [PROXY] 2025/08/15 00:26:12 Received message: Type=pong, ID=heartbeat
2025-08-15 00:26:39,993 - AI-Service - INFO - [PROXY] 2025/08/15 00:26:39 Received message: Type=pong, ID=heartbeat
2025-08-15 00:26:42,947 - AI-Service - INFO - [PROXY] 2025/08/15 00:26:42 Received message: Type=pong, ID=heartbeat
2025-08-15 00:27:09,992 - AI-Service - INFO - [PROXY] 2025/08/15 00:27:09 Received message: Type=pong, ID=heartbeat
2025-08-15 00:27:12,946 - AI-Service - INFO - [PROXY] 2025/08/15 00:27:12 Received message: Type=pong, ID=heartbeat
2025-08-15 00:27:40,181 - AI-Service - INFO - [PROXY] 2025/08/15 00:27:40 Received message: Type=pong, ID=heartbeat
2025-08-15 00:27:43,111 - AI-Service - INFO - [PROXY] 2025/08/15 00:27:43 Received message: Type=pong, ID=heartbeat
2025-08-15 00:28:09,992 - AI-Service - INFO - [PROXY] 2025/08/15 00:28:09 Received message: Type=pong, ID=heartbeat
2025-08-15 00:28:12,947 - AI-Service - INFO - [PROXY] 2025/08/15 00:28:12 Received message: Type=pong, ID=heartbeat
2025-08-15 00:28:39,993 - AI-Service - INFO - [PROXY] 2025/08/15 00:28:39 Received message: Type=pong, ID=heartbeat
2025-08-15 00:28:42,947 - AI-Service - INFO - [PROXY] 2025/08/15 00:28:42 Received message: Type=pong, ID=heartbeat
2025-08-15 00:29:09,993 - AI-Service - INFO - [PROXY] 2025/08/15 00:29:09 Received message: Type=pong, ID=heartbeat
2025-08-15 00:29:12,947 - AI-Service - INFO - [PROXY] 2025/08/15 00:29:12 Received message: Type=pong, ID=heartbeat
2025-08-15 00:29:39,993 - AI-Service - INFO - [PROXY] 2025/08/15 00:29:39 Received message: Type=pong, ID=heartbeat
2025-08-15 00:29:42,947 - AI-Service - INFO - [PROXY] 2025/08/15 00:29:42 Received message: Type=pong, ID=heartbeat
2025-08-15 00:30:09,993 - AI-Service - INFO - [PROXY] 2025/08/15 00:30:09 Received message: Type=pong, ID=heartbeat
2025-08-15 00:30:12,948 - AI-Service - INFO - [PROXY] 2025/08/15 00:30:12 Received message: Type=pong, ID=heartbeat
2025-08-15 00:30:45,145 - AI-Service - INFO - Started proxy process with PID: 42, cmd: /usr/local/lib/python3.12/dist-packages/BaseOS/ai/proxy/BaseOS.Desktop.TunnelGateway -server ************* -port 8090 -host 127.0.0.1 -manager 8090 -type AI -name MGS86 -group MGS
2025-08-15 00:30:45,149 - AI-Service - INFO - [PROXY] 2025/08/15 00:30:45 Registering client...
2025-08-15 00:30:45,149 - AI-Service - INFO - [PROXY] 2025/08/15 00:30:45 Connecting to server: http://*************:8090/register?name=MGS86&type=AI&id=39982ac2-4d6c-34c0-a0d9-a8e46242846a&ip=**********&group=MGS
2025-08-15 00:30:45,169 - AI-Service - INFO - [PROXY] 2025/08/15 00:30:45 sever response: &{101 Switching Protocols 101 HTTP/1.1 1 1 map[Connection:[Upgrade] Upgrade:[tcp]] {} 0 [] false false map[] 0xc0002ae000 <nil>}
2025-08-15 00:30:45,169 - AI-Service - INFO - [PROXY] 2025/08/15 00:30:45 successfully registered client and established control connection
2025-08-15 00:30:45,169 - AI-Service - INFO - [PROXY] 2025/08/15 00:30:45 Client registered successfully, building global connection manager...
2025-08-15 00:30:45,169 - AI-Service - INFO - [PROXY] 2025/08/15 00:30:45 Global connection manager built, starting API server...
2025-08-15 00:30:45,169 - AI-Service - INFO - [PROXY] 2025/08/15 00:30:45 API server started at 127.0.0.1:8090
2025-08-15 00:30:55,155 - AI-Service - INFO - Service VllmService bound to URL /VllmService/chat/completions
2025-08-15 00:30:55,155 - AI-Service - INFO - Service VllmService bound to URL /VllmService/healthcheck
2025-08-15 00:30:55,156 - AI-Service - INFO - Service VllmService bound to URL /VllmService/demo
2025-08-15 00:30:55,156 - AI-Service - INFO - Service VllmService bound to URL /VllmService/metadata
2025-08-15 00:30:55,156 - AI-Service - INFO - Service VllmService bound to URL /VllmService/home
2025-08-15 00:30:55,156 - AI-Service - INFO - Registering service VllmService to client at http://localhost:8090/url/register with params: {'app_name': 'GPTOSS', 'service_address': '5000', 'service_name': 'VllmService', 'service_group': 'BaseOS.AI.Foundry', 'base_url': '/VllmService/chat/completions', 'api_type': 'AI'}
2025-08-15 00:30:55,195 - AI-Service - INFO - [PROXY] 2025/08/15 00:30:55 URL mapping registered successfully: /Tunnel/AI/MGS/GPTOSS/MGS86
2025-08-15 00:30:55,197 - AI-Service - INFO - Service VllmService registered to client at http://localhost:8090/url/register
2025-08-15 00:30:55,197 - AI-Service - INFO - Response: 200 - {"success":true,"url_path":"/Tunnel/AI/MGS/GPTOSS/MGS86"}

2025-08-15 00:30:55,198 - AI-Service - INFO - request agent register body: {'serverName': 'MGS86_GPTOSS', 'aliasName': 'GPTOSS', 'routers': [{'router': '/chat/completions', 'type': 'Json.BaseOS.AI.Foundry.VllmService', 'address': '/Tunnel/AI/MGS/GPTOSS/MGS86/VllmService', 'models': ['gpt-oss-120b']}], 'groupName': 'MGS', 'appName': 'GPTOSS', 'clientName': 'MGS86'}
2025-08-15 00:30:55,198 - AI-Service - INFO - service ip: **************, port: 60000
2025-08-15 00:30:55,272 - AI-Service - INFO - Services registered successfully
2025-08-15 00:30:55,272 - AI-Service - INFO - Heartbeat service started
2025-08-15 00:31:15,180 - AI-Service - INFO - [PROXY] 2025/08/15 00:31:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:31:45,207 - AI-Service - INFO - [PROXY] 2025/08/15 00:31:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:32:15,302 - AI-Service - INFO - [PROXY] 2025/08/15 00:32:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:32:45,180 - AI-Service - INFO - [PROXY] 2025/08/15 00:32:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:33:15,179 - AI-Service - INFO - [PROXY] 2025/08/15 00:33:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:33:45,197 - AI-Service - INFO - [PROXY] 2025/08/15 00:33:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:34:15,209 - AI-Service - INFO - [PROXY] 2025/08/15 00:34:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:34:45,207 - AI-Service - INFO - [PROXY] 2025/08/15 00:34:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:35:15,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:35:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:35:45,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:35:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:36:15,183 - AI-Service - INFO - [PROXY] 2025/08/15 00:36:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:36:45,195 - AI-Service - INFO - [PROXY] 2025/08/15 00:36:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:37:15,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:37:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:37:45,363 - AI-Service - INFO - [PROXY] 2025/08/15 00:37:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:38:15,186 - AI-Service - INFO - [PROXY] 2025/08/15 00:38:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:38:45,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:38:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:39:15,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:39:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:39:45,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:39:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:40:15,207 - AI-Service - INFO - [PROXY] 2025/08/15 00:40:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:40:17,907 - AI-Service - INFO - [PROXY] 2025/08/15 00:40:17 Received message: Type=tcp_open, ID=tcp_1755243617885945000_127.0.0.1:56105
2025-08-15 00:40:17,907 - AI-Service - INFO - [PROXY] 2025/08/15 00:40:17 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:40:45,205 - AI-Service - INFO - [PROXY] 2025/08/15 00:40:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:41:15,207 - AI-Service - INFO - [PROXY] 2025/08/15 00:41:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:41:33,834 - AI-Service - INFO - [PROXY] 2025/08/15 00:41:33 Received message: Type=tcp_open, ID=tcp_1755243693814187100_127.0.0.1:56125
2025-08-15 00:41:33,834 - AI-Service - INFO - [PROXY] 2025/08/15 00:41:33 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:41:45,189 - AI-Service - INFO - [PROXY] 2025/08/15 00:41:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:41:57,517 - AI-Service - INFO - [PROXY] 2025/08/15 00:41:57 Received message: Type=tcp_open, ID=tcp_1755243717497299400_127.0.0.1:56129
2025-08-15 00:41:57,517 - AI-Service - INFO - [PROXY] 2025/08/15 00:41:57 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:42:15,333 - AI-Service - INFO - [PROXY] 2025/08/15 00:42:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:42:21,119 - AI-Service - INFO - [PROXY] 2025/08/15 00:42:21 Received message: Type=tcp_open, ID=tcp_1755243741099726200_127.0.0.1:56140
2025-08-15 00:42:21,119 - AI-Service - INFO - [PROXY] 2025/08/15 00:42:21 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:42:31,369 - AI-Service - INFO - [PROXY] 2025/08/15 00:42:31 Received message: Type=tcp_open, ID=tcp_1755243751350356900_127.0.0.1:56144
2025-08-15 00:42:31,370 - AI-Service - INFO - [PROXY] 2025/08/15 00:42:31 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:42:45,182 - AI-Service - INFO - [PROXY] 2025/08/15 00:42:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:43:15,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:43:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:43:45,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:43:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:44:15,192 - AI-Service - INFO - [PROXY] 2025/08/15 00:44:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:44:45,188 - AI-Service - INFO - [PROXY] 2025/08/15 00:44:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:45:15,207 - AI-Service - INFO - [PROXY] 2025/08/15 00:45:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:45:45,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:45:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:46:15,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:46:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:46:45,186 - AI-Service - INFO - [PROXY] 2025/08/15 00:46:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:47:15,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:47:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:47:18,666 - AI-Service - INFO - [PROXY] 2025/08/15 00:47:18 Received message: Type=tcp_open, ID=tcp_1755244038649183900_127.0.0.1:56244
2025-08-15 00:47:18,666 - AI-Service - INFO - [PROXY] 2025/08/15 00:47:18 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 00:47:45,360 - AI-Service - INFO - [PROXY] 2025/08/15 00:47:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:48:15,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:48:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:48:45,181 - AI-Service - INFO - [PROXY] 2025/08/15 00:48:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:49:15,192 - AI-Service - INFO - [PROXY] 2025/08/15 00:49:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:49:45,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:49:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:50:15,220 - AI-Service - INFO - [PROXY] 2025/08/15 00:50:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:50:45,182 - AI-Service - INFO - [PROXY] 2025/08/15 00:50:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:51:15,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:51:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:51:45,181 - AI-Service - INFO - [PROXY] 2025/08/15 00:51:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:52:15,347 - AI-Service - INFO - [PROXY] 2025/08/15 00:52:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:52:45,184 - AI-Service - INFO - [PROXY] 2025/08/15 00:52:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:53:15,210 - AI-Service - INFO - [PROXY] 2025/08/15 00:53:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:53:45,208 - AI-Service - INFO - [PROXY] 2025/08/15 00:53:45 Received message: Type=pong, ID=heartbeat
2025-08-15 00:54:15,193 - AI-Service - INFO - [PROXY] 2025/08/15 00:54:15 Received message: Type=pong, ID=heartbeat
2025-08-15 00:55:20,358 - AI-Service - INFO - Started proxy process with PID: 42, cmd: /usr/local/lib/python3.12/dist-packages/BaseOS/ai/proxy/BaseOS.Desktop.TunnelGateway -server ************* -port 8090 -host 127.0.0.1 -manager 8090 -type AI -name MGS86 -group MGS
2025-08-15 00:55:20,363 - AI-Service - INFO - [PROXY] 2025/08/15 00:55:20 Registering client...
2025-08-15 00:55:20,363 - AI-Service - INFO - [PROXY] 2025/08/15 00:55:20 Connecting to server: http://*************:8090/register?name=MGS86&type=AI&id=bd868d79-672e-31a0-bae6-fd3c35be74f4&ip=**********&group=MGS
2025-08-15 00:55:20,389 - AI-Service - INFO - [PROXY] 2025/08/15 00:55:20 sever response: &{101 Switching Protocols 101 HTTP/1.1 1 1 map[Connection:[Upgrade] Upgrade:[tcp]] {} 0 [] false false map[] 0xc00022e000 <nil>}
2025-08-15 00:55:20,397 - AI-Service - INFO - [PROXY] 2025/08/15 00:55:20 successfully registered client and established control connection
2025-08-15 00:55:20,397 - AI-Service - INFO - [PROXY] 2025/08/15 00:55:20 Client registered successfully, building global connection manager...
2025-08-15 00:55:20,397 - AI-Service - INFO - [PROXY] 2025/08/15 00:55:20 Global connection manager built, starting API server...
2025-08-15 00:55:20,397 - AI-Service - INFO - [PROXY] 2025/08/15 00:55:20 API server started at 127.0.0.1:8090
2025-08-15 00:55:30,368 - AI-Service - INFO - Service VllmService bound to URL /VllmService/chat/completions
2025-08-15 00:55:30,369 - AI-Service - INFO - Service VllmService bound to URL /VllmService/healthcheck
2025-08-15 00:55:30,369 - AI-Service - INFO - Service VllmService bound to URL /VllmService/demo
2025-08-15 00:55:30,369 - AI-Service - INFO - Service VllmService bound to URL /VllmService/metadata
2025-08-15 00:55:30,369 - AI-Service - INFO - Service VllmService bound to URL /VllmService/home
2025-08-15 00:55:30,369 - AI-Service - INFO - Registering service VllmService to client at http://localhost:8090/url/register with params: {'app_name': 'GPTOSS', 'service_address': '5000', 'service_name': 'VllmService', 'service_group': 'BaseOS.AI.Foundry', 'base_url': '/VllmService/chat/completions', 'api_type': 'AI'}
2025-08-15 00:55:30,400 - AI-Service - INFO - [PROXY] 2025/08/15 00:55:30 URL mapping registered successfully: /Tunnel/AI/MGS/GPTOSS/MGS86
2025-08-15 00:55:30,402 - AI-Service - INFO - Service VllmService registered to client at http://localhost:8090/url/register
2025-08-15 00:55:30,402 - AI-Service - INFO - Response: 200 - {"success":true,"url_path":"/Tunnel/AI/MGS/GPTOSS/MGS86"}

2025-08-15 00:55:30,403 - AI-Service - INFO - request agent register body: {'serverName': 'MGS86_GPTOSS', 'aliasName': 'GPTOSS', 'routers': [{'router': '/chat/completions', 'type': 'Json.BaseOS.AI.Foundry.VllmService', 'address': '/Tunnel/AI/MGS/GPTOSS/MGS86/VllmService', 'models': ['gpt-oss-120b']}], 'groupName': 'MGS', 'appName': 'GPTOSS', 'clientName': 'MGS86'}
2025-08-15 00:55:30,403 - AI-Service - INFO - service ip: **************, port: 60000
2025-08-15 00:55:30,497 - AI-Service - INFO - Services registered successfully
2025-08-15 00:55:30,497 - AI-Service - INFO - Heartbeat service started
2025-08-15 00:55:50,424 - AI-Service - INFO - [PROXY] 2025/08/15 00:55:50 Received message: Type=pong, ID=heartbeat
2025-08-15 00:56:20,433 - AI-Service - INFO - [PROXY] 2025/08/15 00:56:20 Received message: Type=pong, ID=heartbeat
2025-08-15 00:56:50,433 - AI-Service - INFO - [PROXY] 2025/08/15 00:56:50 Received message: Type=pong, ID=heartbeat
2025-08-15 00:57:20,423 - AI-Service - INFO - [PROXY] 2025/08/15 00:57:20 Received message: Type=pong, ID=heartbeat
2025-08-15 00:57:50,431 - AI-Service - INFO - [PROXY] 2025/08/15 00:57:50 Received message: Type=pong, ID=heartbeat
2025-08-15 00:58:20,422 - AI-Service - INFO - [PROXY] 2025/08/15 00:58:20 Received message: Type=pong, ID=heartbeat
2025-08-15 00:58:50,434 - AI-Service - INFO - [PROXY] 2025/08/15 00:58:50 Received message: Type=pong, ID=heartbeat
2025-08-15 00:59:20,410 - AI-Service - INFO - [PROXY] 2025/08/15 00:59:20 Received message: Type=pong, ID=heartbeat
2025-08-15 00:59:50,433 - AI-Service - INFO - [PROXY] 2025/08/15 00:59:50 Received message: Type=pong, ID=heartbeat
2025-08-15 01:00:20,405 - AI-Service - INFO - [PROXY] 2025/08/15 01:00:20 Received message: Type=pong, ID=heartbeat
2025-08-15 01:00:50,453 - AI-Service - INFO - [PROXY] 2025/08/15 01:00:50 Received message: Type=pong, ID=heartbeat
2025-08-15 01:01:20,434 - AI-Service - INFO - [PROXY] 2025/08/15 01:01:20 Received message: Type=pong, ID=heartbeat
2025-08-15 01:01:39,476 - AI-Service - INFO - [PROXY] 2025/08/15 01:01:39 Received message: Type=tcp_open, ID=tcp_1755244899462798100_127.0.0.1:56662
2025-08-15 01:01:39,477 - AI-Service - INFO - [PROXY] 2025/08/15 01:01:39 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 01:01:50,411 - AI-Service - INFO - [PROXY] 2025/08/15 01:01:50 Received message: Type=pong, ID=heartbeat
2025-08-15 01:01:52,849 - AI-Service - INFO - [PROXY] 2025/08/15 01:01:52 Received message: Type=tcp_open, ID=tcp_1755244912835205900_127.0.0.1:56673
2025-08-15 01:01:52,849 - AI-Service - INFO - [PROXY] 2025/08/15 01:01:52 Handling TCP proxy connection: appName=GPTOSS, baseURL=/VllmService/chat/completions
2025-08-15 01:02:20,630 - AI-Service - INFO - [PROXY] 2025/08/15 01:02:20 Received message: Type=pong, ID=heartbeat
2025-08-15 01:02:50,417 - AI-Service - INFO - [PROXY] 2025/08/15 01:02:50 Received message: Type=pong, ID=heartbeat
2025-08-15 01:03:30,613 - AI-Service - INFO - Started proxy process with PID: 42, cmd: /usr/local/lib/python3.12/dist-packages/BaseOS/ai/proxy/BaseOS.Desktop.TunnelGateway -server ************* -port 8090 -host 127.0.0.1 -manager 8090 -type AI -name MGS86 -group MGS
2025-08-15 01:03:30,617 - AI-Service - INFO - [PROXY] 2025/08/15 01:03:30 Registering client...
2025-08-15 01:03:30,617 - AI-Service - INFO - [PROXY] 2025/08/15 01:03:30 Connecting to server: http://*************:8090/register?name=MGS86&type=AI&id=456ad69b-016e-3f50-a448-1b3b2785ecfc&ip=**********&group=MGS
2025-08-15 01:03:30,644 - AI-Service - INFO - [PROXY] 2025/08/15 01:03:30 sever response: &{101 Switching Protocols 101 HTTP/1.1 1 1 map[Connection:[Upgrade] Upgrade:[tcp]] {} 0 [] false false map[] 0xc0001f6000 <nil>}
2025-08-15 01:03:30,644 - AI-Service - INFO - [PROXY] 2025/08/15 01:03:30 successfully registered client and established control connection
2025-08-15 01:03:30,644 - AI-Service - INFO - [PROXY] 2025/08/15 01:03:30 Client registered successfully, building global connection manager...
2025-08-15 01:03:30,644 - AI-Service - INFO - [PROXY] 2025/08/15 01:03:30 Global connection manager built, starting API server...
2025-08-15 01:03:30,644 - AI-Service - INFO - [PROXY] 2025/08/15 01:03:30 API server started at 127.0.0.1:8090
2025-08-15 01:03:40,623 - AI-Service - INFO - Service VllmService bound to URL /VllmService/chat/completions
2025-08-15 01:03:40,623 - AI-Service - INFO - Service VllmService bound to URL /VllmService/healthcheck
2025-08-15 01:03:40,623 - AI-Service - INFO - Service VllmService bound to URL /VllmService/demo
2025-08-15 01:03:40,623 - AI-Service - INFO - Service VllmService bound to URL /VllmService/metadata
2025-08-15 01:03:40,623 - AI-Service - INFO - Service VllmService bound to URL /VllmService/home
2025-08-15 01:03:40,623 - AI-Service - INFO - Registering service VllmService to client at http://localhost:8090/url/register with params: {'app_name': 'GPTOSS', 'service_address': '5000', 'service_name': 'VllmService', 'service_group': 'BaseOS.AI.Foundry', 'base_url': '/VllmService/chat/completions', 'api_type': 'AI'}
2025-08-15 01:03:40,653 - AI-Service - INFO - Service VllmService registered to client at http://localhost:8090/url/register
2025-08-15 01:03:40,654 - AI-Service - INFO - Response: 500 - register url mapping failed: url path already registered by another client: /Tunnel/AI/MGS/GPTOSS/MGS86


2025-08-15 01:03:40,654 - AI-Service - ERROR - Failed to register service VllmService to client: Expecting value: line 1 column 1 (char 0)
2025-08-15 01:03:40,654 - AI-Service - INFO - request agent register body: {'serverName': 'MGS86_GPTOSS', 'aliasName': 'GPTOSS', 'routers': [], 'groupName': 'MGS', 'appName': 'GPTOSS', 'clientName': 'MGS86'}
2025-08-15 01:03:40,655 - AI-Service - INFO - service ip: **************, port: 60000
2025-08-15 01:03:40,655 - AI-Service - ERROR - No routers to register to Agent.
2025-08-15 01:04:00,758 - AI-Service - INFO - [PROXY] 2025/08/15 01:04:00 Received message: Type=pong, ID=heartbeat
2025-08-15 01:04:30,686 - AI-Service - INFO - [PROXY] 2025/08/15 01:04:30 Received message: Type=pong, ID=heartbeat
2025-08-15 01:05:00,685 - AI-Service - INFO - [PROXY] 2025/08/15 01:05:00 Received message: Type=pong, ID=heartbeat
2025-08-15 01:05:15,078 - AI-Service - INFO - Started proxy process with PID: 42, cmd: /usr/local/lib/python3.12/dist-packages/BaseOS/ai/proxy/BaseOS.Desktop.TunnelGateway -server ************* -port 8090 -host 127.0.0.1 -manager 8090 -type AI -name MGS86 -group MGS
2025-08-15 01:05:15,082 - AI-Service - INFO - [PROXY] 2025/08/15 01:05:15 Registering client...
2025-08-15 01:05:15,082 - AI-Service - INFO - [PROXY] 2025/08/15 01:05:15 Connecting to server: http://*************:8090/register?name=MGS86&type=AI&id=ffeb6d0a-90f0-3fa9-ba06-13c752769cb9&ip=**********&group=MGS
2025-08-15 01:05:15,105 - AI-Service - INFO - [PROXY] 2025/08/15 01:05:15 sever response: &{101 Switching Protocols 101 HTTP/1.1 1 1 map[Connection:[Upgrade] Upgrade:[tcp]] {} 0 [] false false map[] 0xc00025e000 <nil>}
2025-08-15 01:05:15,105 - AI-Service - INFO - [PROXY] 2025/08/15 01:05:15 successfully registered client and established control connection
2025-08-15 01:05:15,105 - AI-Service - INFO - [PROXY] 2025/08/15 01:05:15 Client registered successfully, building global connection manager...
2025-08-15 01:05:15,105 - AI-Service - INFO - [PROXY] 2025/08/15 01:05:15 Global connection manager built, starting API server...
2025-08-15 01:05:15,105 - AI-Service - INFO - [PROXY] 2025/08/15 01:05:15 API server started at 127.0.0.1:8090
2025-08-15 01:05:25,088 - AI-Service - INFO - Service VllmService bound to URL /VllmService/chat/completions
2025-08-15 01:05:25,088 - AI-Service - INFO - Service VllmService bound to URL /VllmService/healthcheck
2025-08-15 01:05:25,088 - AI-Service - INFO - Service VllmService bound to URL /VllmService/demo
2025-08-15 01:05:25,088 - AI-Service - INFO - Service VllmService bound to URL /VllmService/metadata
2025-08-15 01:05:25,088 - AI-Service - INFO - Service VllmService bound to URL /VllmService/home
2025-08-15 01:05:25,088 - AI-Service - INFO - Registering service VllmService to client at http://localhost:8090/url/register with params: {'app_name': 'GPTOSS', 'service_address': '5000', 'service_name': 'VllmService', 'service_group': 'BaseOS.AI.Foundry', 'base_url': '/VllmService/chat/completions', 'api_type': 'AI'}
2025-08-15 01:05:25,275 - AI-Service - INFO - Service VllmService registered to client at http://localhost:8090/url/register
2025-08-15 01:05:25,275 - AI-Service - INFO - Response: 500 - register url mapping failed: url path already registered by another client: /Tunnel/AI/MGS/GPTOSS/MGS86


2025-08-15 01:05:25,275 - AI-Service - ERROR - Failed to register service VllmService to client: Expecting value: line 1 column 1 (char 0)
2025-08-15 01:05:25,276 - AI-Service - INFO - request agent register body: {'serverName': 'MGS86_GPTOSS', 'aliasName': 'GPTOSS', 'routers': [], 'groupName': 'MGS', 'appName': 'GPTOSS', 'clientName': 'MGS86'}
2025-08-15 01:05:25,276 - AI-Service - INFO - service ip: **************, port: 60000
2025-08-15 01:05:25,276 - AI-Service - ERROR - No routers to register to Agent.
2025-08-15 01:05:45,136 - AI-Service - INFO - [PROXY] 2025/08/15 01:05:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:06:15,262 - AI-Service - INFO - [PROXY] 2025/08/15 01:06:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:06:45,199 - AI-Service - INFO - [PROXY] 2025/08/15 01:06:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:07:15,243 - AI-Service - INFO - [PROXY] 2025/08/15 01:07:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:07:45,279 - AI-Service - INFO - [PROXY] 2025/08/15 01:07:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:08:15,146 - AI-Service - INFO - [PROXY] 2025/08/15 01:08:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:08:45,145 - AI-Service - INFO - [PROXY] 2025/08/15 01:08:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:09:15,145 - AI-Service - INFO - [PROXY] 2025/08/15 01:09:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:09:45,143 - AI-Service - INFO - [PROXY] 2025/08/15 01:09:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:10:15,148 - AI-Service - INFO - [PROXY] 2025/08/15 01:10:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:10:45,122 - AI-Service - INFO - [PROXY] 2025/08/15 01:10:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:11:15,145 - AI-Service - INFO - [PROXY] 2025/08/15 01:11:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:11:45,146 - AI-Service - INFO - [PROXY] 2025/08/15 01:11:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:12:15,159 - AI-Service - INFO - [PROXY] 2025/08/15 01:12:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:12:45,143 - AI-Service - INFO - [PROXY] 2025/08/15 01:12:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:13:15,123 - AI-Service - INFO - [PROXY] 2025/08/15 01:13:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:13:45,144 - AI-Service - INFO - [PROXY] 2025/08/15 01:13:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:14:15,117 - AI-Service - INFO - [PROXY] 2025/08/15 01:14:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:14:45,120 - AI-Service - INFO - [PROXY] 2025/08/15 01:14:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:15:15,122 - AI-Service - INFO - [PROXY] 2025/08/15 01:15:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:15:45,136 - AI-Service - INFO - [PROXY] 2025/08/15 01:15:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:16:15,116 - AI-Service - INFO - [PROXY] 2025/08/15 01:16:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:16:45,145 - AI-Service - INFO - [PROXY] 2025/08/15 01:16:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:17:15,126 - AI-Service - INFO - [PROXY] 2025/08/15 01:17:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:17:45,218 - AI-Service - INFO - [PROXY] 2025/08/15 01:17:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:18:15,121 - AI-Service - INFO - [PROXY] 2025/08/15 01:18:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:18:45,150 - AI-Service - INFO - [PROXY] 2025/08/15 01:18:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:19:15,145 - AI-Service - INFO - [PROXY] 2025/08/15 01:19:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:19:45,116 - AI-Service - INFO - [PROXY] 2025/08/15 01:19:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:20:15,120 - AI-Service - INFO - [PROXY] 2025/08/15 01:20:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:20:45,145 - AI-Service - INFO - [PROXY] 2025/08/15 01:20:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:21:15,146 - AI-Service - INFO - [PROXY] 2025/08/15 01:21:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:21:45,119 - AI-Service - INFO - [PROXY] 2025/08/15 01:21:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:22:15,265 - AI-Service - INFO - [PROXY] 2025/08/15 01:22:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:22:45,117 - AI-Service - INFO - [PROXY] 2025/08/15 01:22:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:23:15,117 - AI-Service - INFO - [PROXY] 2025/08/15 01:23:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:23:45,145 - AI-Service - INFO - [PROXY] 2025/08/15 01:23:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:24:15,146 - AI-Service - INFO - [PROXY] 2025/08/15 01:24:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:24:45,123 - AI-Service - INFO - [PROXY] 2025/08/15 01:24:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:25:15,116 - AI-Service - INFO - [PROXY] 2025/08/15 01:25:15 Received message: Type=pong, ID=heartbeat
2025-08-15 01:25:45,146 - AI-Service - INFO - [PROXY] 2025/08/15 01:25:45 Received message: Type=pong, ID=heartbeat
2025-08-15 01:26:15,145 - AI-Service - INFO - [PROXY] 2025/08/15 01:26:15 Received message: Type=pong, ID=heartbeat
