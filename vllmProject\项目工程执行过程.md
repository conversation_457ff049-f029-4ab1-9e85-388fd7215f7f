# 项目工程执行过程

## 1. 环境变量设置

在拉取 vllm 镜像时，可以设置如下环境变量：

- `VLLM_COMMIT`：用于指定 vllm 镜像的具体版本（commit hash）。
  
示例设置命令：

```sh
export VLLM_COMMIT=33f460b17a54acb3b6cc0b03f4a17876cff5eafd
```

## 1. 拉取 vllm 镜像

根据历史命令，拉取镜像的方式如下：

- 拉取官方 vllm 镜像：

```sh
docker pull registry.openwebgis.com/llm/vllm:1.0
```

- 拉取指定 commit 的 vllm 镜像：

```sh
docker pull public.ecr.aws/q9t5s3a7/vllm-ci-postmerge-repo:${VLLM_COMMIT}
```

- 拉取自定义 registry 镜像：

```sh
docker pull registry.openwebgis.com/llm/vllm:1.0
docker pull registry.openwebgis.com/llm/vllm:2.0
```

## 3. 镜像构建（如需本地构建）

如果需要本地构建镜像，可以使用如下命令：

```sh
sudo docker build -t registry.openwebgis.com/llm/vllm:2.0 .
```

## 4. 启动服务

- 使用 docker-compose 启动服务：

```sh
sudo docker-compose up -d
```

- 或直接运行脚本：

```sh
sudo bash run_as_docker.sh
```

## 5. 查看服务日志

```sh
sudo docker logs -f vllm_vllm-openai_1
```

---

**相关文件参考：**

- docker-compose.yaml
- run_as_docker.sh
- run.sh

如需进一步配置环境变量，可参考 .env 文件内容。