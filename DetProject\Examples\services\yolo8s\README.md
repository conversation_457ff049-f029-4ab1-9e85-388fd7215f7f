---
tasks:
- image-object-detection
widgets:
  - task: image-object-detection
    inputs:
      - type: image
domain:
- cv
frameworks:
- pytorch
metrics:
- P,R,AP
license: MIT License
tags:
- yolov8
- ultralytics
---

# yolov8 模型介绍

## 模型描述

YOLOv8是一款尖端的、最先进的 (SOTA) 模型，它以之前 YOLO 版本的成功为基础，并引入了新功能和改进，以进一步提高性能和灵活性。YOLOv8 旨在快速、准确且易于使用，使其成为各种对象检测和跟踪、实例分割、图像分类和姿势估计任务的绝佳选择。

![tasks](banner-tasks.png)
![comparison](yolo-comparison-plots.png)

- [代码地址](https://github.com/ultralytics/ultralytics)

## 模型效果
![效果](res.jpg)

## 数据集
来源很多种数据源多种尺寸与场景图片总计：
- 图片总数量：**6547张**。
- 标注框数量：**12300个**。
- 来源1：网络数据集，并处理一些过小或者标注不对的数据，火焰烟雾领域几乎都扒拉一下。
- 来源2：自建数据集，**手工标注（你没有听错：人工智能最重要的是人工不是智能）**。

## 模型使用范围
本模型可以检测输入图片中火焰和烟雾的位置。

## 使用方式
- 推理：输入图片，如存在火焰和烟雾则返回火焰和烟雾位置。

## 目标场景
- 火焰和烟雾相关的能力，可应用于复杂场景。

## 模型局限性及可能偏差
- 当前版本在**Python-3.10.14,torch-2.3.1+cu121**环境测试通过，其他环境下可用性待测试。

## 代码示例
```python
### 基于ultralytics推理
### 安装依赖：pip install modelscope ultralytics

from pathlib import Path
from ultralytics import YOLO
from modelscope import snapshot_download

model_dir = snapshot_download('qianliyx/yolov8s-firesmokedet')
modelfile = Path(model_dir).joinpath('pytorch_model.onnx')
model = YOLO(modelfile)
imgpath = '/path/to/you/image'
res = model.predict(imgpath)
for line in res:
    print(line.boxes)

```

## 模型训练流程
- 取用Ultralytics YOLOv8s-v8.2.0训练100个epoch得到。

## 标签结果

| 模型 | 类别 | 名称 |
|------------|------------|------------|
| yolov8s | 0 | fire |
| yolov8s | 1 | smoke |
## 数据集使用申请
| 数据集名称 | 图片数量(张) | 项目链接 |
|------------|------------|------------|
|人脸检测|345264|[项目地址](https://www.modelscope.cn/models/qianliyx/yolov8s-facedet)|
|火焰烟雾检测|6547|[项目地址](https://www.modelscope.cn/models/qianliyx/yolov8s-firesmokedet)|
|人体检测|86166|下方邮箱申请|
|车辆检测|16977|下方邮箱申请|
|口罩检测|8996|下方邮箱申请|
|安全帽检测|8219|下方邮箱申请|
|人员跌倒|7722|下方邮箱申请|
|抽烟检测|5172|下方邮箱申请|
|电动车检测|3836|下方邮箱申请|
|睡岗检测|1365|下方邮箱申请|
|打电话检测|1328|下方邮箱申请|

- 请发送邮件到：<EMAIL>，申请数据集。