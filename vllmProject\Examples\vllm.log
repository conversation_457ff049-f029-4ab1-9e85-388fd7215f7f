INFO 08-15 01:05:26 [__init__.py:241] Automatically detected platform cuda.
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:05:29 [api_server.py:1787] vLLM API server version 0.10.2.dev2+gf5635d62e.d20250807
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:05:29 [utils.py:326] non-default args: {'chat_template': '/work/gpt_oss_120b/chat_template.jinja', 'model': '/work/gpt_oss_120b', 'served_model_name': ['gpt-oss-120b'], 'tensor_parallel_size': 4}
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:05:37 [config.py:726] Resolved architecture: GptOssForCausalLM
[1;36m(APIServer pid=40)[0;0m ERROR 08-15 01:05:37 [config.py:123] Error retrieving safetensors: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/work/gpt_oss_120b'. Use `repo_type` argument if needed., retrying 1 of 2
[1;36m(APIServer pid=40)[0;0m ERROR 08-15 01:05:39 [config.py:121] Error retrieving safetensors: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/work/gpt_oss_120b'. Use `repo_type` argument if needed.
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:05:39 [config.py:3628] Downcasting torch.float32 to torch.bfloat16.
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:05:39 [config.py:1759] Using max model len 131072
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:05:40 [config.py:1198] mxfp4 quantization is not fully optimized yet. The speed can be slower than non-quantized models.
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:05:40 [config.py:2588] Chunked prefill is enabled with max_num_batched_tokens=2048.
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:05:41 [config.py:244] Overriding cuda graph sizes to [1, 2, 4, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112, 120, 128, 136, 144, 152, 160, 168, 176, 184, 192, 200, 208, 216, 224, 232, 240, 248, 256, 272, 288, 304, 320, 336, 352, 368, 384, 400, 416, 432, 448, 464, 480, 496, 512, 528, 544, 560, 576, 592, 608, 624, 640, 656, 672, 688, 704, 720, 736, 752, 768, 784, 800, 816, 832, 848, 864, 880, 896, 912, 928, 944, 960, 976, 992, 1008, 1024]
INFO 08-15 01:05:46 [__init__.py:241] Automatically detected platform cuda.
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:05:48 [core.py:654] Waiting for init message from front-end.
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:05:48 [core.py:73] Initializing a V1 LLM engine (v0.10.2.dev2+gf5635d62e.d20250807) with config: model='/work/gpt_oss_120b', speculative_config=None, tokenizer='/work/gpt_oss_120b', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config={}, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=131072, download_dir=None, load_format=auto, tensor_parallel_size=4, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=mxfp4, enforce_eager=False, kv_cache_dtype=auto, device_config=cuda, decoding_config=DecodingConfig(backend='auto', disable_fallback=False, disable_any_whitespace=False, disable_additional_properties=False, reasoning_backend='openai'), observability_config=ObservabilityConfig(show_hidden_metrics_for_version=None, otlp_traces_endpoint=None, collect_detailed_traces=None), seed=0, served_model_name=gpt-oss-120b, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=True, chunked_prefill_enabled=True, use_async_output_proc=True, pooler_config=None, compilation_config={"level":3,"debug_dump_path":"","cache_dir":"","backend":"","custom_ops":[],"splitting_ops":["vllm.unified_attention","vllm.unified_attention_with_output","vllm.mamba_mixer2"],"use_inductor":true,"compile_sizes":[],"inductor_compile_config":{"enable_auto_functionalized_v2":false},"inductor_passes":{},"use_cudagraph":true,"cudagraph_num_of_warmups":1,"cudagraph_capture_sizes":[1024,1008,992,976,960,944,928,912,896,880,864,848,832,816,800,784,768,752,736,720,704,688,672,656,640,624,608,592,576,560,544,528,512,496,480,464,448,432,416,400,384,368,352,336,320,304,288,272,256,248,240,232,224,216,208,200,192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],"cudagraph_copy_inputs":false,"full_cuda_graph":false,"max_capture_size":1024,"local_cache_dir":null}
[1;36m(EngineCore_0 pid=312)[0;0m 
[1;36m(EngineCore_0 pid=312)[0;0m              LL          LL          MMM       MMM 
[1;36m(EngineCore_0 pid=312)[0;0m              LL          LL          MMMM     MMMM
[1;36m(EngineCore_0 pid=312)[0;0m          V   LL          LL          MM MM   MM MM
[1;36m(EngineCore_0 pid=312)[0;0m vvvv  VVVV   LL          LL          MM  MM MM  MM
[1;36m(EngineCore_0 pid=312)[0;0m vvvv VVVV    LL          LL          MM   MMM   MM
[1;36m(EngineCore_0 pid=312)[0;0m  vvv VVVV    LL          LL          MM    M    MM
[1;36m(EngineCore_0 pid=312)[0;0m   vvVVVV     LL          LL          MM         MM
[1;36m(EngineCore_0 pid=312)[0;0m     VVVV     LLLLLLLLLL  LLLLLLLLL   M           M
[1;36m(EngineCore_0 pid=312)[0;0m 
[1;36m(EngineCore_0 pid=312)[0;0m WARNING 08-15 01:05:48 [multiproc_worker_utils.py:273] Reducing Torch parallelism from 32 threads to 1 to avoid unnecessary CPU contention. Set OMP_NUM_THREADS in the external environment to tune this value as needed.
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:05:48 [shm_broadcast.py:289] vLLM message queue communication handle: Handle(local_reader_ranks=[0, 1, 2, 3], buffer_handle=(4, 16777216, 10, 'psm_0a86da4b'), local_subscribe_addr='ipc:///tmp/23344d70-ab2e-4b2c-95f0-60c0e64b0de5', remote_subscribe_addr=None, remote_addr_ipv6=False)
INFO 08-15 01:05:52 [__init__.py:241] Automatically detected platform cuda.
INFO 08-15 01:05:52 [__init__.py:241] Automatically detected platform cuda.
INFO 08-15 01:05:52 [__init__.py:241] Automatically detected platform cuda.
INFO 08-15 01:05:52 [__init__.py:241] Automatically detected platform cuda.
W0815 01:05:55.965000 446 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
W0815 01:05:55.965000 446 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
W0815 01:05:55.966000 449 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
W0815 01:05:55.966000 449 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
W0815 01:05:55.966000 447 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
W0815 01:05:55.966000 447 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
W0815 01:05:55.969000 448 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
W0815 01:05:55.969000 448 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:05:57 [shm_broadcast.py:289] vLLM message queue communication handle: Handle(local_reader_ranks=[0], buffer_handle=(1, 10485760, 10, 'psm_a4c18375'), local_subscribe_addr='ipc:///tmp/c7a600ff-01f3-4dc1-a37c-6320d5dc5d13', remote_subscribe_addr=None, remote_addr_ipv6=False)
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:05:57 [shm_broadcast.py:289] vLLM message queue communication handle: Handle(local_reader_ranks=[0], buffer_handle=(1, 10485760, 10, 'psm_44e29ebb'), local_subscribe_addr='ipc:///tmp/75572164-fc74-4486-887f-87e81a08d825', remote_subscribe_addr=None, remote_addr_ipv6=False)
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:05:57 [shm_broadcast.py:289] vLLM message queue communication handle: Handle(local_reader_ranks=[0], buffer_handle=(1, 10485760, 10, 'psm_c6a1a6f2'), local_subscribe_addr='ipc:///tmp/039b1ac7-afa3-4df2-96cf-cb266bcf69ed', remote_subscribe_addr=None, remote_addr_ipv6=False)
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:05:57 [shm_broadcast.py:289] vLLM message queue communication handle: Handle(local_reader_ranks=[0], buffer_handle=(1, 10485760, 10, 'psm_c31d8b73'), local_subscribe_addr='ipc:///tmp/2c27e0de-bee6-47e6-ad7a-d12555e6327f', remote_subscribe_addr=None, remote_addr_ipv6=False)
[W815 01:05:58.763466366 ProcessGroupNCCL.cpp:915] Warning: TORCH_NCCL_AVOID_RECORD_STREAMS is the default now, this environment variable is thus deprecated. (function operator())
[W815 01:05:58.779798849 ProcessGroupNCCL.cpp:915] Warning: TORCH_NCCL_AVOID_RECORD_STREAMS is the default now, this environment variable is thus deprecated. (function operator())
[W815 01:05:58.105537583 ProcessGroupNCCL.cpp:915] Warning: TORCH_NCCL_AVOID_RECORD_STREAMS is the default now, this environment variable is thus deprecated. (function operator())
[W815 01:05:58.106570704 ProcessGroupNCCL.cpp:915] Warning: TORCH_NCCL_AVOID_RECORD_STREAMS is the default now, this environment variable is thus deprecated. (function operator())
[Gloo] Rank 1 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[Gloo] Rank 0 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[Gloo] Rank 3 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[Gloo] Rank 2 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[Gloo] Rank 0 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[Gloo] Rank 2 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[Gloo] Rank 1 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[Gloo] Rank 3 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:05:58 [__init__.py:1381] Found nccl from library libnccl.so.2
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:05:58 [pynccl.py:70] vLLM is using nccl==2.27.5
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:05:58 [__init__.py:1381] Found nccl from library libnccl.so.2
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:05:58 [pynccl.py:70] vLLM is using nccl==2.27.5
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:05:58 [__init__.py:1381] Found nccl from library libnccl.so.2
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:05:58 [pynccl.py:70] vLLM is using nccl==2.27.5
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:05:58 [__init__.py:1381] Found nccl from library libnccl.so.2
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:05:58 [pynccl.py:70] vLLM is using nccl==2.27.5
[1;36m(VllmWorker TP3 pid=449)[0;0m WARNING 08-15 01:05:59 [custom_all_reduce.py:137] Custom allreduce is disabled because it's not supported on more than two PCIe-only GPUs. To silence this warning, specify disable_custom_all_reduce=True explicitly.
[1;36m(VllmWorker TP0 pid=446)[0;0m WARNING 08-15 01:05:59 [custom_all_reduce.py:137] Custom allreduce is disabled because it's not supported on more than two PCIe-only GPUs. To silence this warning, specify disable_custom_all_reduce=True explicitly.
[1;36m(VllmWorker TP1 pid=447)[0;0m WARNING 08-15 01:05:59 [custom_all_reduce.py:137] Custom allreduce is disabled because it's not supported on more than two PCIe-only GPUs. To silence this warning, specify disable_custom_all_reduce=True explicitly.
[1;36m(VllmWorker TP2 pid=448)[0;0m WARNING 08-15 01:05:59 [custom_all_reduce.py:137] Custom allreduce is disabled because it's not supported on more than two PCIe-only GPUs. To silence this warning, specify disable_custom_all_reduce=True explicitly.
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:05:59 [shm_broadcast.py:289] vLLM message queue communication handle: Handle(local_reader_ranks=[1, 2, 3], buffer_handle=(3, 4194304, 6, 'psm_5de2767d'), local_subscribe_addr='ipc:///tmp/7ed18807-9f19-4f20-9081-c4c7e9e41008', remote_subscribe_addr=None, remote_addr_ipv6=False)
[Gloo] Rank 0 is connected to 0 peer ranks. Expected number of connected peer ranks is : 0
[Gloo] Rank 0 is connected to 0 peer ranks. Expected number of connected peer ranks is : 0
[Gloo] Rank 0 is connected to 0 peer ranks. Expected number of connected peer ranks is : 0
[Gloo] Rank 0 is connected to 0 peer ranks. Expected number of connected peer ranks is : 0
[Gloo] Rank 0 is connected to 0 peer ranks. Expected number of connected peer ranks is : 0
[Gloo] Rank 0 is connected to 0 peer ranks. Expected number of connected peer ranks is : 0
[Gloo] Rank 0 is connected to 0 peer ranks. Expected number of connected peer ranks is : 0
[Gloo] Rank 0 is connected to 0 peer ranks. Expected number of connected peer ranks is : 0
[Gloo] Rank 0 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[Gloo] Rank 1 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[Gloo] Rank 2 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[Gloo] Rank 3 is connected to 3 peer ranks. Expected number of connected peer ranks is : 3
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:05:59 [parallel_state.py:1102] rank 0 in world size 4 is assigned as DP rank 0, PP rank 0, TP rank 0, EP rank 0
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:05:59 [parallel_state.py:1102] rank 2 in world size 4 is assigned as DP rank 0, PP rank 0, TP rank 2, EP rank 2
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:05:59 [parallel_state.py:1102] rank 3 in world size 4 is assigned as DP rank 0, PP rank 0, TP rank 3, EP rank 3
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:05:59 [parallel_state.py:1102] rank 1 in world size 4 is assigned as DP rank 0, PP rank 0, TP rank 1, EP rank 1
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:05:59 [topk_topp_sampler.py:49] Using FlashInfer for top-p & top-k sampling.
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:05:59 [topk_topp_sampler.py:49] Using FlashInfer for top-p & top-k sampling.
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:05:59 [topk_topp_sampler.py:49] Using FlashInfer for top-p & top-k sampling.
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:05:59 [topk_topp_sampler.py:49] Using FlashInfer for top-p & top-k sampling.
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:05:59 [gpu_model_runner.py:1913] Starting to load model /work/gpt_oss_120b...
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:05:59 [gpu_model_runner.py:1913] Starting to load model /work/gpt_oss_120b...
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:05:59 [gpu_model_runner.py:1913] Starting to load model /work/gpt_oss_120b...
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:05:59 [gpu_model_runner.py:1913] Starting to load model /work/gpt_oss_120b...
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:05:59 [gpu_model_runner.py:1945] Loading model from scratch...
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:05:59 [gpu_model_runner.py:1945] Loading model from scratch...
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:05:59 [gpu_model_runner.py:1945] Loading model from scratch...
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:05:59 [gpu_model_runner.py:1945] Loading model from scratch...
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:05:59 [cuda.py:286] Using Triton backend on V1 engine.
[1;36m(VllmWorker TP3 pid=449)[0;0m WARNING 08-15 01:05:59 [rocm.py:29] Failed to import from amdsmi with ModuleNotFoundError("No module named 'amdsmi'")
[1;36m(VllmWorker TP3 pid=449)[0;0m WARNING 08-15 01:05:59 [rocm.py:40] Failed to import from vllm._rocm_C with ModuleNotFoundError("No module named 'vllm._rocm_C'")
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:05:59 [triton_attn.py:263] Using vllm unified attention for TritonAttentionImpl
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:05:59 [cuda.py:286] Using Triton backend on V1 engine.
[1;36m(VllmWorker TP0 pid=446)[0;0m WARNING 08-15 01:05:59 [rocm.py:29] Failed to import from amdsmi with ModuleNotFoundError("No module named 'amdsmi'")
[1;36m(VllmWorker TP0 pid=446)[0;0m WARNING 08-15 01:05:59 [rocm.py:40] Failed to import from vllm._rocm_C with ModuleNotFoundError("No module named 'vllm._rocm_C'")
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:05:59 [triton_attn.py:263] Using vllm unified attention for TritonAttentionImpl
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:05:59 [cuda.py:286] Using Triton backend on V1 engine.
[1;36m(VllmWorker TP2 pid=448)[0;0m WARNING 08-15 01:05:59 [rocm.py:29] Failed to import from amdsmi with ModuleNotFoundError("No module named 'amdsmi'")
[1;36m(VllmWorker TP2 pid=448)[0;0m WARNING 08-15 01:05:59 [rocm.py:40] Failed to import from vllm._rocm_C with ModuleNotFoundError("No module named 'vllm._rocm_C'")
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:05:59 [triton_attn.py:263] Using vllm unified attention for TritonAttentionImpl
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:05:59 [cuda.py:286] Using Triton backend on V1 engine.
[1;36m(VllmWorker TP1 pid=447)[0;0m WARNING 08-15 01:05:59 [rocm.py:29] Failed to import from amdsmi with ModuleNotFoundError("No module named 'amdsmi'")
[1;36m(VllmWorker TP1 pid=447)[0;0m WARNING 08-15 01:05:59 [rocm.py:40] Failed to import from vllm._rocm_C with ModuleNotFoundError("No module named 'vllm._rocm_C'")
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:05:59 [triton_attn.py:263] Using vllm unified attention for TritonAttentionImpl
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:   0% Completed | 0/15 [00:00<?, ?it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:   7% Completed | 1/15 [00:00<00:09,  1.40it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  13% Completed | 2/15 [00:01<00:10,  1.23it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  20% Completed | 3/15 [00:02<00:09,  1.29it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  27% Completed | 4/15 [00:03<00:08,  1.32it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  33% Completed | 5/15 [00:03<00:07,  1.35it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  40% Completed | 6/15 [00:04<00:06,  1.31it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  47% Completed | 7/15 [00:05<00:06,  1.33it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  53% Completed | 8/15 [00:06<00:05,  1.30it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  60% Completed | 9/15 [00:06<00:04,  1.29it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  67% Completed | 10/15 [00:07<00:03,  1.31it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  73% Completed | 11/15 [00:08<00:03,  1.32it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  80% Completed | 12/15 [00:09<00:02,  1.33it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  87% Completed | 13/15 [00:09<00:01,  1.31it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards:  93% Completed | 14/15 [00:10<00:00,  1.29it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards: 100% Completed | 15/15 [00:11<00:00,  1.28it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Loading safetensors checkpoint shards: 100% Completed | 15/15 [00:11<00:00,  1.30it/s]
[1;36m(VllmWorker TP0 pid=446)[0;0m 
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:06:11 [default_loader.py:262] Loading weights took 11.56 seconds
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:06:11 [default_loader.py:262] Loading weights took 12.16 seconds
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:06:11 [default_loader.py:262] Loading weights took 12.21 seconds
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:06:11 [gpu_model_runner.py:1962] Model loading took 16.2918 GiB and 11.916434 seconds
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:06:11 [default_loader.py:262] Loading weights took 12.21 seconds
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:06:12 [gpu_model_runner.py:1962] Model loading took 16.2918 GiB and 12.439598 seconds
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:06:12 [gpu_model_runner.py:1962] Model loading took 16.2918 GiB and 12.489595 seconds
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:06:12 [gpu_model_runner.py:1962] Model loading took 16.2918 GiB and 12.547818 seconds
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:06:18 [backends.py:530] Using cache directory: /root/.cache/vllm/torch_compile_cache/5b15768f06/rank_2_0/backbone for vLLM's torch.compile
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:06:18 [backends.py:541] Dynamo bytecode transform time: 5.68 s
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:06:18 [backends.py:530] Using cache directory: /root/.cache/vllm/torch_compile_cache/5b15768f06/rank_1_0/backbone for vLLM's torch.compile
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:06:18 [backends.py:541] Dynamo bytecode transform time: 5.70 s
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:06:18 [backends.py:530] Using cache directory: /root/.cache/vllm/torch_compile_cache/5b15768f06/rank_3_0/backbone for vLLM's torch.compile
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:06:18 [backends.py:541] Dynamo bytecode transform time: 5.73 s
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:06:18 [backends.py:530] Using cache directory: /root/.cache/vllm/torch_compile_cache/5b15768f06/rank_0_0/backbone for vLLM's torch.compile
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:06:18 [backends.py:541] Dynamo bytecode transform time: 6.00 s
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:06:23 [backends.py:194] Cache the graph for dynamic shape for later use
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:06:23 [backends.py:194] Cache the graph for dynamic shape for later use
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:06:23 [backends.py:194] Cache the graph for dynamic shape for later use
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:06:23 [backends.py:194] Cache the graph for dynamic shape for later use
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:06:59 [backends.py:215] Compiling a graph for dynamic shape takes 40.39 s
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:06:59 [backends.py:215] Compiling a graph for dynamic shape takes 40.58 s
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:06:59 [backends.py:215] Compiling a graph for dynamic shape takes 40.77 s
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:07:00 [backends.py:215] Compiling a graph for dynamic shape takes 40.69 s
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:07:20 [monitor.py:34] torch.compile takes 46.09 s in total
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:07:20 [monitor.py:34] torch.compile takes 46.30 s in total
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:07:20 [monitor.py:34] torch.compile takes 46.69 s in total
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:07:20 [monitor.py:34] torch.compile takes 46.45 s in total
[1;36m(VllmWorker TP3 pid=449)[0;0m [rank3]:W0815 01:07:21.059000 449 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
[1;36m(VllmWorker TP3 pid=449)[0;0m [rank3]:W0815 01:07:21.059000 449 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
[1;36m(VllmWorker TP0 pid=446)[0;0m [rank0]:W0815 01:07:21.059000 446 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
[1;36m(VllmWorker TP0 pid=446)[0;0m [rank0]:W0815 01:07:21.059000 446 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
[1;36m(VllmWorker TP2 pid=448)[0;0m [rank2]:W0815 01:07:21.060000 448 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
[1;36m(VllmWorker TP2 pid=448)[0;0m [rank2]:W0815 01:07:21.060000 448 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
[1;36m(VllmWorker TP3 pid=449)[0;0m [rank3]:W0815 01:07:21.061000 449 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
[1;36m(VllmWorker TP3 pid=449)[0;0m [rank3]:W0815 01:07:21.061000 449 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
[1;36m(VllmWorker TP1 pid=447)[0;0m [rank1]:W0815 01:07:21.062000 447 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
[1;36m(VllmWorker TP1 pid=447)[0;0m [rank1]:W0815 01:07:21.062000 447 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
[1;36m(VllmWorker TP0 pid=446)[0;0m [rank0]:W0815 01:07:21.063000 446 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
[1;36m(VllmWorker TP0 pid=446)[0;0m [rank0]:W0815 01:07:21.063000 446 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
[1;36m(VllmWorker TP2 pid=448)[0;0m [rank2]:W0815 01:07:21.064000 448 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
[1;36m(VllmWorker TP2 pid=448)[0;0m [rank2]:W0815 01:07:21.064000 448 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
[1;36m(VllmWorker TP1 pid=447)[0;0m [rank1]:W0815 01:07:21.066000 447 torch/utils/cpp_extension.py:2425] TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
[1;36m(VllmWorker TP1 pid=447)[0;0m [rank1]:W0815 01:07:21.066000 447 torch/utils/cpp_extension.py:2425] If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'] to specific architectures.
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:08:11 [gpu_worker.py:275] Available KV cache memory: 25.56 GiB
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:08:12 [gpu_worker.py:275] Available KV cache memory: 25.56 GiB
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:08:12 [gpu_worker.py:275] Available KV cache memory: 25.56 GiB
[1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:08:12 [gpu_worker.py:275] Available KV cache memory: 25.56 GiB
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:08:12 [kv_cache_utils.py:993] GPU KV cache size: 1,488,864 tokens
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:08:12 [kv_cache_utils.py:997] Maximum concurrency for 131,072 tokens per request: 22.34x
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:08:12 [kv_cache_utils.py:993] GPU KV cache size: 1,488,864 tokens
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:08:12 [kv_cache_utils.py:997] Maximum concurrency for 131,072 tokens per request: 22.34x
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:08:12 [kv_cache_utils.py:993] GPU KV cache size: 1,488,864 tokens
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:08:12 [kv_cache_utils.py:997] Maximum concurrency for 131,072 tokens per request: 22.34x
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:08:12 [kv_cache_utils.py:993] GPU KV cache size: 1,488,864 tokens
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:08:12 [kv_cache_utils.py:997] Maximum concurrency for 131,072 tokens per request: 22.34x
[1;36m(VllmWorker TP0 pid=446)[0;0m 
Capturing CUDA graph shapes:   0%|          | 0/83 [00:00<?, ?it/s]
Capturing CUDA graph shapes:   1%|          | 1/83 [00:03<05:06,  3.73s/it]
Capturing CUDA graph shapes:   2%|▏         | 2/83 [00:03<02:14,  1.65s/it]
Capturing CUDA graph shapes:   4%|▎         | 3/83 [00:05<02:25,  1.82s/it]
Capturing CUDA graph shapes:   5%|▍         | 4/83 [00:06<01:32,  1.18s/it]
Capturing CUDA graph shapes:   6%|▌         | 5/83 [00:07<01:46,  1.37s/it]
Capturing CUDA graph shapes:   7%|▋         | 6/83 [00:08<01:14,  1.03it/s]
Capturing CUDA graph shapes:   8%|▊         | 7/83 [00:11<02:21,  1.87s/it]
Capturing CUDA graph shapes:  10%|▉         | 8/83 [00:11<01:39,  1.33s/it]
Capturing CUDA graph shapes:  11%|█         | 9/83 [00:14<02:12,  1.79s/it]
Capturing CUDA graph shapes:  12%|█▏        | 10/83 [00:14<01:34,  1.29s/it]
Capturing CUDA graph shapes:  13%|█▎        | 11/83 [00:16<01:40,  1.40s/it]
Capturing CUDA graph shapes:  14%|█▍        | 12/83 [00:16<01:13,  1.03s/it]
Capturing CUDA graph shapes:  16%|█▌        | 13/83 [00:18<01:25,  1.22s/it]
Capturing CUDA graph shapes:  17%|█▋        | 14/83 [00:18<01:02,  1.11it/s]
Capturing CUDA graph shapes:  18%|█▊        | 15/83 [00:20<01:16,  1.13s/it]
Capturing CUDA graph shapes:  19%|█▉        | 16/83 [00:20<00:56,  1.18it/s]
Capturing CUDA graph shapes:  20%|██        | 17/83 [00:22<01:11,  1.08s/it]
Capturing CUDA graph shapes:  22%|██▏       | 18/83 [00:22<00:52,  1.23it/s]
Capturing CUDA graph shapes:  23%|██▎       | 19/83 [00:23<01:07,  1.06s/it]
Capturing CUDA graph shapes:  24%|██▍       | 20/83 [00:24<00:50,  1.26it/s]
Capturing CUDA graph shapes:  25%|██▌       | 21/83 [00:25<01:06,  1.06s/it]
Capturing CUDA graph shapes:  27%|██▋       | 22/83 [00:25<00:48,  1.25it/s]
Capturing CUDA graph shapes:  28%|██▊       | 23/83 [00:27<01:03,  1.05s/it]
Capturing CUDA graph shapes:  29%|██▉       | 24/83 [00:27<00:46,  1.27it/s]
Capturing CUDA graph shapes:  30%|███       | 25/83 [00:29<01:00,  1.04s/it]
Capturing CUDA graph shapes:  31%|███▏      | 26/83 [00:29<00:44,  1.28it/s]
Capturing CUDA graph shapes:  33%|███▎      | 27/83 [00:31<00:57,  1.04s/it]
Capturing CUDA graph shapes:  34%|███▎      | 28/83 [00:31<00:42,  1.29it/s]
Capturing CUDA graph shapes:  35%|███▍      | 29/83 [00:32<00:55,  1.04s/it]
Capturing CUDA graph shapes:  36%|███▌      | 30/83 [00:33<00:41,  1.29it/s]
Capturing CUDA graph shapes:  37%|███▋      | 31/83 [00:34<00:53,  1.04s/it]
Capturing CUDA graph shapes:  39%|███▊      | 32/83 [00:36<01:06,  1.30s/it]
Capturing CUDA graph shapes:  40%|███▉      | 33/83 [00:38<01:09,  1.40s/it]
Capturing CUDA graph shapes:  41%|████      | 34/83 [00:38<00:50,  1.03s/it]
Capturing CUDA graph shapes:  42%|████▏     | 35/83 [00:41<01:23,  1.73s/it]
Capturing CUDA graph shapes:  43%|████▎     | 36/83 [00:42<00:59,  1.26s/it]
Capturing CUDA graph shapes:  45%|████▍     | 37/83 [00:43<01:03,  1.38s/it]
Capturing CUDA graph shapes:  46%|████▌     | 38/83 [00:43<00:45,  1.02s/it]
Capturing CUDA graph shapes:  47%|████▋     | 39/83 [00:45<00:53,  1.21s/it]
Capturing CUDA graph shapes:  48%|████▊     | 40/83 [00:45<00:38,  1.11it/s]
Capturing CUDA graph shapes:  49%|████▉     | 41/83 [00:47<00:47,  1.12s/it]
Capturing CUDA graph shapes:  51%|█████     | 42/83 [00:47<00:34,  1.19it/s]
Capturing CUDA graph shapes:  52%|█████▏    | 43/83 [00:49<00:43,  1.09s/it]
Capturing CUDA graph shapes:  53%|█████▎    | 44/83 [00:49<00:31,  1.23it/s]
Capturing CUDA graph shapes:  54%|█████▍    | 45/83 [00:50<00:40,  1.07s/it]
Capturing CUDA graph shapes:  55%|█████▌    | 46/83 [00:51<00:29,  1.26it/s]
Capturing CUDA graph shapes:  57%|█████▋    | 47/83 [00:52<00:37,  1.05s/it]
Capturing CUDA graph shapes:  58%|█████▊    | 48/83 [00:52<00:27,  1.27it/s]
Capturing CUDA graph shapes:  59%|█████▉    | 49/83 [00:54<00:35,  1.05s/it]
Capturing CUDA graph shapes:  60%|██████    | 50/83 [00:59<01:10,  2.15s/it]
Capturing CUDA graph shapes:  61%|██████▏   | 51/83 [00:59<00:49,  1.55s/it]
Capturing CUDA graph shapes:  63%|██████▎   | 52/83 [00:59<00:35,  1.14s/it]
Capturing CUDA graph shapes:  64%|██████▍   | 53/83 [01:01<00:38,  1.29s/it]
Capturing CUDA graph shapes:  65%|██████▌   | 54/83 [01:02<00:40,  1.39s/it]
Capturing CUDA graph shapes:  66%|██████▋   | 55/83 [01:03<00:28,  1.02s/it]
Capturing CUDA graph shapes:  67%|██████▋   | 56/83 [01:03<00:20,  1.30it/s]
Capturing CUDA graph shapes:  69%|██████▊   | 57/83 [01:04<00:26,  1.03s/it]
Capturing CUDA graph shapes:  70%|██████▉   | 58/83 [01:06<00:31,  1.25s/it]
Capturing CUDA graph shapes:  71%|███████   | 59/83 [01:06<00:22,  1.08it/s]
Capturing CUDA graph shapes:  72%|███████▏  | 60/83 [01:07<00:16,  1.43it/s]
Capturing CUDA graph shapes:  73%|███████▎  | 61/83 [01:08<00:21,  1.02it/s]
Capturing CUDA graph shapes:  75%|███████▍  | 62/83 [01:10<00:24,  1.18s/it]
Capturing CUDA graph shapes:  76%|███████▌  | 63/83 [01:10<00:17,  1.14it/s]
Capturing CUDA graph shapes:  77%|███████▋  | 64/83 [01:10<00:12,  1.50it/s]
Capturing CUDA graph shapes:  78%|███████▊  | 65/83 [01:12<00:17,  1.04it/s]
Capturing CUDA graph shapes:  80%|███████▉  | 66/83 [01:14<00:20,  1.19s/it]
Capturing CUDA graph shapes:  81%|████████  | 67/83 [01:14<00:14,  1.14it/s]
Capturing CUDA graph shapes:  82%|████████▏ | 68/83 [01:14<00:10,  1.50it/s]
Capturing CUDA graph shapes:  83%|████████▎ | 69/83 [01:16<00:13,  1.03it/s]
Capturing CUDA graph shapes:  84%|████████▍ | 70/83 [01:17<00:15,  1.19s/it]
Capturing CUDA graph shapes:  86%|████████▌ | 71/83 [01:17<00:10,  1.13it/s]
Capturing CUDA graph shapes:  87%|████████▋ | 72/83 [01:18<00:07,  1.49it/s]
Capturing CUDA graph shapes:  88%|████████▊ | 73/83 [01:19<00:09,  1.03it/s]
Capturing CUDA graph shapes:  89%|████████▉ | 74/83 [01:21<00:10,  1.18s/it]
Capturing CUDA graph shapes:  90%|█████████ | 75/83 [01:21<00:07,  1.14it/s]
Capturing CUDA graph shapes:  92%|█████████▏| 76/83 [01:21<00:04,  1.50it/s]
Capturing CUDA graph shapes:  93%|█████████▎| 77/83 [01:24<00:06,  1.14s/it]
Capturing CUDA graph shapes:  94%|█████████▍| 78/83 [01:27<00:09,  1.83s/it]
Capturing CUDA graph shapes:  95%|█████████▌| 79/83 [01:28<00:06,  1.70s/it]
Capturing CUDA graph shapes:  96%|█████████▋| 80/83 [01:30<00:04,  1.60s/it]
Capturing CUDA graph shapes:  98%|█████████▊| 81/83 [01:30<00:02,  1.18s/it]
Capturing CUDA graph shapes:  99%|█████████▉| 82/83 [01:33<00:01,  1.79s/it][1;36m(VllmWorker TP2 pid=448)[0;0m INFO 08-15 01:09:53 [gpu_model_runner.py:2567] Graph capturing finished in 101 secs, took 1.81 GiB
[1;36m(VllmWorker TP1 pid=447)[0;0m INFO 08-15 01:09:53 [gpu_model_runner.py:2567] Graph capturing finished in 101 secs, took 1.81 GiB

Capturing CUDA graph shapes: 100%|██████████| 83/83 [01:40<00:00,  3.28s/it]
Capturing CUDA graph shapes: 100%|██████████| 83/83 [01:40<00:00,  1.21s/it]
[1;36m(VllmWorker TP0 pid=446)[0;0m INFO 08-15 01:09:53 [gpu_model_runner.py:2567] Graph capturing finished in 101 secs, took 1.81 GiB
[1;36m(VllmWorker TP3 pid=449)[0;0m INFO 08-15 01:09:53 [gpu_model_runner.py:2567] Graph capturing finished in 101 secs, took 1.81 GiB
[1;36m(EngineCore_0 pid=312)[0;0m INFO 08-15 01:09:53 [core.py:216] init engine (profile, create kv cache, warmup model) took 220.84 seconds
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:09:54 [loggers.py:142] Engine 000: vllm cache_config_info with initialization after num_gpu_blocks is: 186109
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:09:54 [api_server.py:1599] Supported_tasks: ['generate']
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:09:55 [serving_responses.py:123] For gpt-oss, we ignore --enable-auto-tool-choice and always enable tool use.
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [api_server.py:1857] Starting vLLM API server 0 on http://0.0.0.0:8000
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:29] Available routes are:
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /openapi.json, Methods: HEAD, GET
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /docs, Methods: HEAD, GET
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /docs/oauth2-redirect, Methods: HEAD, GET
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /redoc, Methods: HEAD, GET
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /health, Methods: GET
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /load, Methods: GET
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /ping, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /ping, Methods: GET
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /tokenize, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /detokenize, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/models, Methods: GET
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /version, Methods: GET
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/responses, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/responses/{response_id}, Methods: GET
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/responses/{response_id}/cancel, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/chat/completions, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/completions, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/embeddings, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /pooling, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /classify, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /score, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/score, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/audio/transcriptions, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/audio/translations, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /rerank, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v1/rerank, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /v2/rerank, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /scale_elastic_ep, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /is_scaling_elastic_ep, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /invocations, Methods: POST
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:14 [launcher.py:37] Route: /metrics, Methods: GET
[1;36m(APIServer pid=40)[0;0m INFO:     Started server process [40]
[1;36m(APIServer pid=40)[0;0m INFO:     Waiting for application startup.
[1;36m(APIServer pid=40)[0;0m INFO:     Application startup complete.
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:11:47 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:59088 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[1;36m(APIServer pid=40)[0;0m ERROR 08-15 01:11:52 [serving_chat.py:1001] Error in chat completion stream generator.
[1;36m(APIServer pid=40)[0;0m ERROR 08-15 01:11:52 [serving_chat.py:1001] Traceback (most recent call last):
[1;36m(APIServer pid=40)[0;0m ERROR 08-15 01:11:52 [serving_chat.py:1001]   File "/usr/local/lib/python3.12/dist-packages/vllm/entrypoints/openai/serving_chat.py", line 642, in chat_completion_stream_generator
[1;36m(APIServer pid=40)[0;0m ERROR 08-15 01:11:52 [serving_chat.py:1001]     harmony_parser.process(token_id)
[1;36m(APIServer pid=40)[0;0m ERROR 08-15 01:11:52 [serving_chat.py:1001]   File "/usr/local/lib/python3.12/dist-packages/openai_harmony/__init__.py", line 612, in process
[1;36m(APIServer pid=40)[0;0m ERROR 08-15 01:11:52 [serving_chat.py:1001]     self._inner.process(token)
[1;36m(APIServer pid=40)[0;0m ERROR 08-15 01:11:52 [serving_chat.py:1001] openai_harmony.HarmonyError: Unexpected token 173781 while expecting start token 200006
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:11:54 [loggers.py:123] Engine 000: Avg prompt throughput: 271.0 tokens/s, Avg generation throughput: 12.4 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.1%, Prefix cache hit rate: 0.0%
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:12:04 [loggers.py:123] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.1%, Prefix cache hit rate: 0.0%
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m WARNING 08-15 01:12:45 [protocol.py:74] The following fields were present in the request but ignored: {'strict'}
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:54850 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:12:54 [loggers.py:123] Engine 000: Avg prompt throughput: 271.0 tokens/s, Avg generation throughput: 20.2 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 49.9%
[1;36m(APIServer pid=40)[0;0m INFO 08-15 01:13:04 [loggers.py:123] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 49.9%
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:47650 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:47666 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:47682 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:47688 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:41252 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:41262 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:41264 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:41270 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:41280 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:41288 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:41300 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:41314 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:57958 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:57970 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
[1;36m(APIServer pid=40)[0;0m INFO:     127.0.0.1:57982 - "POST /v1/chat/completions HTTP/1.1" 400 Bad Request
