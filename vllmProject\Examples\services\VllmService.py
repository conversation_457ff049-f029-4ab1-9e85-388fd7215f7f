from flask import jsonify, Response, stream_with_context
from BaseOS.ai.framework import ModelService,SVREGISTRY
from BaseOS.ai.framework.utils import get_logger
import requests

logger = get_logger()

@SVREGISTRY.register_module("VllmService")
class VllmService(ModelService):
    def __init__(self, config,upload_dir):
        """
        初始化服务对象
        :param config: server配置字典对象
        """
        super().__init__(config,upload_dir)

    def predict(self, request):
        try:
            vllm_url = "http://localhost:8000/v1/chat/completions"
            payload = request.get_json(force=True)
            headers = {"Content-Type": "application/json"}
            # 判断是否需要流式转发
            stream = payload.get("stream", False)
            resp = requests.post(vllm_url, json=payload, headers=headers, stream=stream)
            if stream:
                def generate():
                    for chunk in resp.iter_content(chunk_size=None):
                        if chunk:
                            yield chunk
                return Response(stream_with_context(generate()), content_type=resp.headers.get("Content-Type", "application/json"), status=resp.status_code)
            else:
                return jsonify(resp.json()), resp.status_code
        except Exception as e:
            logger.error(f"Error processing JSON: {str(e)}")
            return jsonify({'error': str(e)}), 500




