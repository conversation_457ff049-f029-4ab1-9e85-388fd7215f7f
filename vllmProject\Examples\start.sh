#!/bin/bash

# Function to set environment variables
set_environment_variable() {
    local env_name="$1"
    local env_value="$2"

    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
        # Windows environment
        existing_value=$(powershell -Command "[Environment]::GetEnvironmentVariable('$env_name', 'Machine')")
        if [ ! -z "$existing_value" ]; then
            echo "$env_name already set to $existing_value, skipping overwrite."
            return
        fi
        powershell -Command "[Environment]::SetEnvironmentVariable('$env_name', '$env_value', 'Machine')"
    else
        # Linux environment
        local env_path="/BaseOS/.env"
        local content="${env_name}=\"${env_value}\""

        # 写入系统环境变量
        export "${env_name}=${env_value}"

        if [ ! -f "$env_path" ]; then
            echo "$content" > "$env_path"
        elif grep -q "^${env_name}=" "$env_path"; then
            echo "$env_name already set in $env_path, skipping overwrite."
        else
            echo "$content" >> "$env_path"
        fi
    fi
}

# Generate BASEOSNODEID
generate_baseos_node_id() {
    local node_id=""
    
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
        # Windows 环境：尝试从系统环境变量读取
        node_id=$(powershell -Command "[Environment]::GetEnvironmentVariable('BASEOSNODEID', 'Machine')")
        if [ ! -z "$node_id" ]; then
            echo "$node_id"
            return
        fi
    else
        # Linux/Unix 环境：先看当前环境变量
        if [ ! -z "${BASEOSNODEID}" ]; then
            echo "$BASEOSNODEID"
            return
        fi

        # 再看 /BaseOS/.env 文件
        local env_path="/BaseOS/.env"
        mkdir -p "$(dirname "$env_path")"
        if [ -f "$env_path" ]; then
            local existing_value=$(grep '^BASEOSNODEID=' "$env_path" | cut -d'=' -f2- | tr -d '"')
            if [ ! -z "$existing_value" ]; then
                echo "$existing_value"
                return
            fi
        fi
    fi

    # 都没有时，生成新 ID
    local machine_name=$(hostname)
    local date_part=$(date +"%Y%m%d")
    local random_part=$(shuf -i 1000-9999 -n 1)
    echo "${machine_name}${date_part}${random_part}"
}

# Set BASEOSNODEID environment variable
BASEOSNODEID=$(generate_baseos_node_id)
set_environment_variable "BASEOSNODEID" "$BASEOSNODEID"


CONFIG_FILE="./config.json"
# 提取 host
APP_HOST=$(grep '"host"' "$CONFIG_FILE" | head -1 | sed 's/.*: *"\(.*\)",*/\1/')
APP_HOST=$(echo "$APP_HOST" | tr -d '\r' | sed 's/[[:space:]]*$//')
# 提取 port
APP_PORT=$(grep '"port"' "$CONFIG_FILE" | head -1 | sed 's/.*: *\([0-9]*\),*/\1/')
APP_PORT=$(echo "$APP_PORT" | tr -d '\r'| sed 's/[[:space:]]*$//')




# 启动vllm
export VLLM_ATTENTION_BACKEND=TRITON_ATTN_VLLM_V1 && nohup python3 -m vllm.entrypoints.openai.api_server \
                                                    --model /work/gpt_oss_120b \
                                                    --tensor-parallel-size 4 \
                                                    --served-model-name gpt-oss-120b \
                                                    --chat-template /work/gpt_oss_120b/chat_template.jinja > vllm.log 2>&1 &

# 启动进程
gunicorn --workers=1 --threads=1 --worker-class=gthread --bind="$APP_HOST":"$APP_PORT" app:app

