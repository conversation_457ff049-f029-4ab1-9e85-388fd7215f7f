{"app": {"name": "flask-server", "name_zh": "样例服务", "host": "0.0.0.0", "port": 5000, "version": "1.0.0", "description": "A simple Flask server application"}, "register": {"porxy_client_name": "MGS53", "porxy_client_group": "MGS", "porxy_client_port": 8090, "porxy_server_ip": "*************", "porxy_server_port": 8090, "agent_register_url": "", "agent_heartbeat_url": ""}, "provider": {"name": "mgs", "phone": "xxxxxxxxxxx", "email": "<EMAIL>"}, "logging": {"level": "DEBUG", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "filename": "app.log", "max_bytes": 10485760, "backup_count": 5}, "services": {"FireSmokeDetService": {"name_zh": "火焰烟雾检测", "name": "FireSmokeDetService", "description": "检测图片中的火焰烟雾，返回带检测框的图片及检测框信息", "input": {"input_image": "image"}, "parameters": {"conf": 0.4, "category": {"0": "fire", "1": "smoke"}}, "output": {"res_image": "image"}, "models": ["small"], "type": "Image", "subtype": "ImageDetection", "url": "/FireSmogDet"}}}